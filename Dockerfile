# Usa l'immagine base di Kali Linux
FROM kalilinux/kali-rolling

# Aggiorna il sistema e installa le dipendenze necessarie
RUN apt-get update && apt-get install -y \
    python3 \
    python3-venv \
    python3-pip \
    python3-dev \
    wget \
    curl \
    git \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Imposta la directory di lavoro
WORKDIR /app

# Copia l'intera cartella heka
COPY . /app/heka/

# Cambia nella directory heka
WORKDIR /app/heka

# Crea un ambiente virtuale Python
RUN python3 -m venv venv

# Attiva l'ambiente virtuale e installa le dipendenze
RUN . venv/bin/activate && \
    pip install --upgrade pip && \
    pip install -r requirements.txt

# Installa i browser per Playwright
RUN . venv/bin/activate && playwright install --with-deps



# Comando di default
CMD ["/bin/bash"]
