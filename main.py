#!/usr/bin/env python3
"""
Heka Collaborative Agent System - Main Entry Point
Sistema di 7 agenti collaborativi per penetration testing automatizzato
"""

import asyncio
import logging
import sys
import argparse
import os
import time
from typing import Dict, Any
from datetime import datetime

from agents.core.agent_coordinator import AgentCoordinator, CoordinationStrategy

# Colori per output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Configurazione logging
class ColoredFormatter(logging.Formatter):
    """Formatter colorato per i log"""

    COLORS = {
        'DEBUG': Colors.OKCYAN,
        'INFO': Colors.OKGREEN,
        'WARNING': Colors.WARNING,
        'ERROR': Colors.FAIL,
        'CRITICAL': Colors.FAIL + Colors.BOLD
    }

    def format(self, record):
        log_color = self.COLORS.get(record.levelname, '')
        record.levelname = f"{log_color}{record.levelname}{Colors.ENDC}"
        return super().format(record)

# Setup logging con colori
def setup_logging(verbose=False):
    level = logging.DEBUG if verbose else logging.INFO

    # Handler per console con colori
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(ColoredFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))

    # Handler per file senza colori
    file_handler = logging.FileHandler('heka.log')
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))

    # Configura root logger
    logging.basicConfig(
        level=level,
        handlers=[console_handler, file_handler]
    )

def print_banner():
    """Stampa il banner di Heka"""
    banner = f"""
{Colors.HEADER}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                        ██╗  ██╗███████╗██╗  ██╗ █████╗                       ║
║                        ██║  ██║██╔════╝██║ ██╔╝██╔══██╗                      ║
║                        ███████║█████╗  █████╔╝ ███████║                      ║
║                        ██╔══██║██╔══╝  ██╔═██╗ ██╔══██║                      ║
║                        ██║  ██║███████╗██║  ██╗██║  ██║                      ║
║                        ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝                      ║
║                                                                              ║
║                     Collaborative Agent System v1.0 by B0                    ║
║                  Sistema di 7 Agenti per Penetration Testing                 ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
{Colors.ENDC}"""
    print(banner)

def print_section(title, color=Colors.OKBLUE):
    """Stampa una sezione colorata"""
    print(f"\n{color}{Colors.BOLD}{'='*80}")
    print(f"  {title}")
    print(f"{'='*80}{Colors.ENDC}")

def print_subsection(title, color=Colors.OKCYAN):
    """Stampa una sottosezione colorata"""
    print(f"\n{color}{Colors.BOLD}{'-'*60}")
    print(f"  {title}")
    print(f"{'-'*60}{Colors.ENDC}")

def print_status(message, status="info"):
    """Stampa un messaggio di stato colorato"""
    colors = {
        "info": Colors.OKBLUE,
        "success": Colors.OKGREEN,
        "warning": Colors.WARNING,
        "error": Colors.FAIL
    }
    icons = {
        "info": "ℹ️",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌"
    }

    color = colors.get(status, Colors.OKBLUE)
    icon = icons.get(status, "•")

    print(f"{color}{icon} {message}{Colors.ENDC}")

class HekaSystem:
    """Sistema principale Heka"""

    def __init__(self):
        self.coordinator = AgentCoordinator()
        self.logger = logging.getLogger("heka.system")
        self.a5_output_callback = None

    def set_a5_output_callback(self, callback):
        """Imposta callback per output A5 in tempo reale"""
        self.a5_output_callback = callback

    async def initialize(self):
        """Inizializza il sistema completo"""
        try:
            print_status("Inizializzazione sistema Heka...", "info")

            # Inizializza tutti i 7 agenti
            await self.coordinator.initialize_agents()

            # Imposta callback per A5 se disponibile
            if "a5_terminal_executor" in self.coordinator.agents and self.a5_output_callback:
                a5_agent = self.coordinator.agents["a5_terminal_executor"]
                a5_agent.set_output_callback(self.a5_output_callback)

            print_status("Sistema Heka inizializzato con successo!", "success")

            print_subsection("Agenti Disponibili", Colors.OKGREEN)
            for agent_id, agent in self.coordinator.agents.items():
                print(f"   {Colors.OKGREEN}•{Colors.ENDC} {agent.name} ({Colors.OKCYAN}{agent_id}{Colors.ENDC})")

        except Exception as e:
            print_status(f"Errore inizializzazione sistema: {e}", "error")
            raise

    async def run_penetration_test(self, target: str, objective: str,
                                 strategy: CoordinationStrategy = CoordinationStrategy.ADAPTIVE) -> Dict[str, Any]:
        """Esegue un penetration test completo"""
        try:
            print_section("🎯 AVVIO PENETRATION TEST", Colors.HEADER)
            print(f"   {Colors.BOLD}Target:{Colors.ENDC} {Colors.OKCYAN}{target}{Colors.ENDC}")
            print(f"   {Colors.BOLD}Obiettivo:{Colors.ENDC} {objective}")
            print(f"   {Colors.BOLD}Strategia:{Colors.ENDC} {Colors.WARNING}{strategy.value.upper()}{Colors.ENDC}")
            print(f"   {Colors.BOLD}Timestamp:{Colors.ENDC} {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # Esegui task collaborativo
            result = await self.coordinator.execute_collaborative_task(
                objective=objective,
                target=target,
                strategy=strategy
            )

            if result.get("success", False):
                print_status("Penetration test completato con successo!", "success")

                # Mostra riassunto risultati
                self._show_results_summary(result)

            else:
                print_status(f"Penetration test fallito: {result.get('error', 'Unknown error')}", "error")

            return result

        except Exception as e:
            print_status(f"Errore durante penetration test: {e}", "error")
            return {"success": False, "error": str(e)}

    def _show_results_summary(self, result: Dict[str, Any]):
        """Mostra riassunto dei risultati"""
        print_section("📊 RIASSUNTO RISULTATI", Colors.OKGREEN)

        # Risultati per agente
        agent_results = result.get("agent_results", {})

        print_subsection("Stato Agenti", Colors.OKBLUE)
        for agent_id, agent_result in agent_results.items():
            if agent_result.get("success"):
                execution_time = agent_result.get("execution_time", 0)
                print(f"   {Colors.OKGREEN}✅{Colors.ENDC} {Colors.BOLD}{agent_id}{Colors.ENDC}: completato in {Colors.OKCYAN}{execution_time:.2f}s{Colors.ENDC}")
            else:
                error = agent_result.get("error", "Unknown error")
                print(f"   {Colors.FAIL}❌{Colors.ENDC} {Colors.BOLD}{agent_id}{Colors.ENDC}: {Colors.FAIL}{error}{Colors.ENDC}")

        # Statistiche generali
        total_time = result.get("total_execution_time", 0)
        print(f"\n   {Colors.WARNING}⏱️{Colors.ENDC}  {Colors.BOLD}Tempo totale:{Colors.ENDC} {Colors.OKCYAN}{total_time:.2f}s{Colors.ENDC}")

        # Risultati specifici
        if "a6_task_monitor" in agent_results:
            monitor_data = agent_results["a6_task_monitor"].get("data", {})
            objective_reached = monitor_data.get("objective_reached", False)
            completion_score = monitor_data.get("completion_score", 0.0)

            print_subsection("Analisi Completamento", Colors.WARNING)
            status_icon = "✅" if objective_reached else "⚠️"
            status_text = "Sì" if objective_reached else "Parziale"
            print(f"   🎯 {Colors.BOLD}Obiettivo raggiunto:{Colors.ENDC} {status_icon} {status_text}")
            print(f"   📈 {Colors.BOLD}Score completamento:{Colors.ENDC} {Colors.OKCYAN}{completion_score:.1%}{Colors.ENDC}")

        if "a7_report_generator" in agent_results:
            report_data = agent_results["a7_report_generator"].get("data", {})
            if report_data.get("report_generated"):
                markdown_report = report_data.get("markdown_report")
                pdf_report = report_data.get("pdf_report")

                print_subsection("Report Generati", Colors.OKGREEN)
                if markdown_report:
                    print(f"   📄 {Colors.BOLD}Markdown:{Colors.ENDC} {Colors.OKCYAN}{markdown_report}{Colors.ENDC}")
                if pdf_report:
                    print(f"   📄 {Colors.BOLD}PDF:{Colors.ENDC} {Colors.OKCYAN}{pdf_report}{Colors.ENDC}")

    async def shutdown(self):
        """Chiude il sistema"""
        try:
            print_status("Chiusura sistema Heka...", "info")

            # Chiudi tutti gli agenti
            for agent in self.coordinator.agents.values():
                await agent.shutdown()

            print_status("Sistema Heka chiuso correttamente", "success")

        except Exception as e:
            print_status(f"Errore chiusura sistema: {e}", "error")

def a5_output_callback(output_line):
    """Callback per output A5 in tempo reale"""
    if output_line.strip():
        line = output_line.strip()

        # Colori diversi per diversi tipi di output
        if line.startswith("[COMANDO AVVIATO]"):
            print(f"{Colors.WARNING}🚀 {line}{Colors.ENDC}")
        elif line.startswith("[PROCESSO COMPLETATO]"):
            print(f"{Colors.OKGREEN}✅ {line}{Colors.ENDC}")
        elif line.startswith("[STDERR]"):
            print(f"{Colors.FAIL}❌ {line}{Colors.ENDC}")
        else:
            print(f"{Colors.OKCYAN}📟 [A5 LIVE]{Colors.ENDC} {line}")

def show_main_menu():
    """Mostra il menu principale"""
    print_section("🎮 MENU PRINCIPALE", Colors.HEADER)
    print(f"   {Colors.BOLD}1.{Colors.ENDC} Configura e avvia penetration test")
    print(f"   {Colors.BOLD}2.{Colors.ENDC} Test rapido con impostazioni predefinite")
    print(f"   {Colors.BOLD}3.{Colors.ENDC} Mostra stato sistema")
    print(f"   {Colors.BOLD}4.{Colors.ENDC} Informazioni sistema")
    print(f"   {Colors.BOLD}5.{Colors.ENDC} Esci")
    print()

def get_user_input(prompt, default=None, choices=None):
    """Ottiene input dall'utente con validazione"""
    while True:
        if default:
            user_input = input(f"{Colors.OKCYAN}{prompt} [{default}]:{Colors.ENDC} ").strip()
            if not user_input:
                return default
        else:
            user_input = input(f"{Colors.OKCYAN}{prompt}:{Colors.ENDC} ").strip()

        if not user_input and not default:
            print_status("Input richiesto!", "error")
            continue

        if choices and user_input not in choices:
            print_status(f"Scelta non valida. Opzioni: {', '.join(choices)}", "error")
            continue

        return user_input

def configure_test():
    """Configura un test personalizzato"""
    print_section("⚙️ CONFIGURAZIONE TEST", Colors.WARNING)

    # Target
    target = get_user_input("Target (IP o dominio)")

    # Obiettivo
    print(f"\n{Colors.BOLD}Obiettivi predefiniti:{Colors.ENDC}")
    objectives = {
        "1": "Reconnaissance e information gathering completo",
        "2": "Vulnerability assessment di rete",
        "3": "Test sicurezza applicazione web",
        "4": "Penetration test completo",
        "5": "Personalizzato"
    }

    for key, value in objectives.items():
        print(f"   {Colors.BOLD}{key}.{Colors.ENDC} {value}")

    obj_choice = get_user_input("Scegli obiettivo (1-5)", "1", list(objectives.keys()))

    if obj_choice == "5":
        objective = get_user_input("Inserisci obiettivo personalizzato")
    else:
        objective = objectives[obj_choice]

    # Strategia
    print(f"\n{Colors.BOLD}Strategie di esecuzione:{Colors.ENDC}")
    strategies = {
        "1": ("sequential", "Sequenziale - Agenti eseguono uno dopo l'altro"),
        "2": ("parallel", "Parallelo - Alcuni agenti eseguono in parallelo"),
        "3": ("adaptive", "Adattivo - Strategia ottimizzata automaticamente")
    }

    for key, (name, desc) in strategies.items():
        print(f"   {Colors.BOLD}{key}.{Colors.ENDC} {name.upper()} - {desc}")

    strat_choice = get_user_input("Scegli strategia (1-3)", "3", list(strategies.keys()))
    strategy_name = strategies[strat_choice][0]

    # Opzioni avanzate
    print(f"\n{Colors.BOLD}Opzioni avanzate:{Colors.ENDC}")
    verbose = get_user_input("Output verboso? (s/n)", "n", ["s", "n"]) == "s"
    live_output = get_user_input("Output A5 in tempo reale? (s/n)", "s", ["s", "n"]) == "s"

    return {
        "target": target,
        "objective": objective,
        "strategy": strategy_name,
        "verbose": verbose,
        "live_output": live_output
    }

def show_system_info():
    """Mostra informazioni sul sistema"""
    print_section("ℹ️ INFORMAZIONI SISTEMA", Colors.OKBLUE)

    print(f"   {Colors.BOLD}Versione:{Colors.ENDC} 1.0")
    print(f"   {Colors.BOLD}Agenti:{Colors.ENDC} 7 agenti collaborativi")
    print(f"   {Colors.BOLD}Linguaggio:{Colors.ENDC} Python 3.8+")
    print(f"   {Colors.BOLD}Sistema:{Colors.ENDC} Kali Linux (raccomandato)")

    print_subsection("Agenti Disponibili", Colors.OKCYAN)
    agents_info = [
        ("A1", "Prompt Generator & LLM Interface", "Comunicazione con AI"),
        ("A2", "Web Research & Navigator", "Ricerca web e dorking"),
        ("A3", "Command Generator", "Generazione comandi Kali"),
        ("A4", "Code Generator", "Generazione script e codice"),
        ("A5", "Terminal Executor", "Esecuzione comandi sicura"),
        ("A6", "Task Completion Monitor", "Monitoraggio obiettivi"),
        ("A7", "PDF Report Generator", "Generazione report")
    ]

    for agent_id, name, desc in agents_info:
        print(f"   {Colors.OKGREEN}{agent_id}{Colors.ENDC} - {Colors.BOLD}{name}{Colors.ENDC}")
        print(f"       {desc}")

async def run_interactive_mode():
    """Modalità interattiva migliorata"""
    print_banner()

    while True:
        try:
            show_main_menu()
            choice = get_user_input("Scegli un'opzione (1-5)", choices=["1", "2", "3", "4", "5"])

            if choice == "1":
                # Configura test personalizzato
                config = configure_test()
                await run_configured_test(config)

            elif choice == "2":
                # Test rapido
                target = get_user_input("Target per test rapido")
                config = {
                    "target": target,
                    "objective": "Reconnaissance e vulnerability assessment completo",
                    "strategy": "adaptive",
                    "verbose": False,
                    "live_output": True
                }
                await run_configured_test(config)

            elif choice == "3":
                # Stato sistema
                print_status("Funzionalità non ancora implementata", "warning")

            elif choice == "4":
                # Informazioni sistema
                show_system_info()

            elif choice == "5":
                # Esci
                print_status("Arrivederci!", "info")
                break

            # Pausa prima del prossimo menu
            input(f"\n{Colors.OKCYAN}Premi INVIO per continuare...{Colors.ENDC}")

        except KeyboardInterrupt:
            print(f"\n{Colors.WARNING}👋 Arrivederci!{Colors.ENDC}")
            break
        except Exception as e:
            print_status(f"Errore: {e}", "error")

async def run_configured_test(config):
    """Esegue un test con la configurazione specificata"""
    # Setup logging
    setup_logging(config["verbose"])

    # Mappa strategia
    strategy_map = {
        "sequential": CoordinationStrategy.SEQUENTIAL,
        "parallel": CoordinationStrategy.PARALLEL,
        "adaptive": CoordinationStrategy.ADAPTIVE
    }
    strategy = strategy_map[config["strategy"]]

    # Inizializza sistema
    heka = HekaSystem()

    # Imposta callback per output A5 se richiesto
    if config["live_output"]:
        heka.set_a5_output_callback(a5_output_callback)

    try:
        # Inizializza
        await heka.initialize()

        # Esegui penetration test
        result = await heka.run_penetration_test(
            target=config["target"],
            objective=config["objective"],
            strategy=strategy
        )

        if result.get("success"):
            print_status("Test completato con successo!", "success")
        else:
            print_status(f"Test fallito: {result.get('error', 'Unknown error')}", "error")

    except KeyboardInterrupt:
        print_status("Test interrotto dall'utente", "warning")
    except Exception as e:
        print_status(f"Errore durante il test: {e}", "error")
    finally:
        await heka.shutdown()

async def main():
    """Funzione principale per modalità CLI"""
    parser = argparse.ArgumentParser(description="Heka Collaborative Agent System")
    parser.add_argument("target", nargs='?', help="Target del penetration test (IP o dominio)")
    parser.add_argument("--objective", "-o",
                       default="Eseguire reconnaissance e vulnerability assessment completo",
                       help="Obiettivo del penetration test")
    parser.add_argument("--strategy", "-s",
                       choices=["sequential", "parallel", "adaptive"],
                       default="adaptive",
                       help="Strategia di coordinamento agenti")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Output verboso")
    parser.add_argument("--live-output", "-l", action="store_true",
                       help="Output A5 in tempo reale")

    args = parser.parse_args()

    # Se nessun target, avvia modalità interattiva
    if not args.target:
        await run_interactive_mode()
        return

    # Modalità CLI
    setup_logging(args.verbose)

    # Mappa strategia
    strategy_map = {
        "sequential": CoordinationStrategy.SEQUENTIAL,
        "parallel": CoordinationStrategy.PARALLEL,
        "adaptive": CoordinationStrategy.ADAPTIVE
    }
    strategy = strategy_map[args.strategy]

    # Inizializza sistema
    heka = HekaSystem()

    # Imposta callback per output A5 se richiesto
    if args.live_output:
        heka.set_a5_output_callback(a5_output_callback)

    try:
        print_banner()

        # Inizializza
        await heka.initialize()

        # Esegui penetration test
        result = await heka.run_penetration_test(
            target=args.target,
            objective=args.objective,
            strategy=strategy
        )

        # Determina exit code
        exit_code = 0 if result.get("success", False) else 1

    except KeyboardInterrupt:
        print_status("Interruzione utente", "warning")
        exit_code = 130
    except Exception as e:
        print_status(f"Errore fatale: {e}", "error")
        exit_code = 1
    finally:
        # Chiudi sistema
        await heka.shutdown()

    sys.exit(exit_code)

if __name__ == "__main__":
    asyncio.run(main())