# Heka Collaborative Agent System - Dependencies

# Core dependencies
asyncio-mqtt>=0.11.1
aiohttp>=3.8.0
aiofiles>=22.1.0

# Web automation and research (Agent A2)
playwright>=1.40.0
beautifulsoup4>=4.12.0
requests>=2.31.0
selenium>=4.15.0

# AI/LLM integration (Agent A1)
openai>=1.3.0
anthropic>=0.7.0
google-generativeai>=0.3.0

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0
lxml>=4.9.0

# Report generation (Agent A7)
reportlab>=4.0.0
markdown>=3.5.0
jinja2>=3.1.0
weasyprint>=60.0

# Network and security tools integration
python-nmap>=0.7.1
scapy>=2.5.0
dnspython>=2.4.0

# Logging and monitoring
structlog>=23.1.0
colorama>=0.4.6

# Configuration and environment
python-dotenv>=1.0.0
pyyaml>=6.0.1
toml>=0.10.2

# Testing and development
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
black>=23.7.0
flake8>=6.0.0

# Optional dependencies for enhanced functionality
# Uncomment if needed:

# Advanced web scraping
# scrapy>=2.11.0
# selenium-wire>=5.1.0

# Additional AI providers
# cohere>=4.32.0
# huggingface-hub>=0.17.0

# Database support
# sqlalchemy>=2.0.0
# aiosqlite>=0.19.0

# Advanced networking
# paramiko>=3.3.0
# netaddr>=0.9.0

# Image processing for screenshots
# pillow>=10.0.0

# Advanced parsing
# pypdf>=3.16.0
# python-docx>=0.8.11

# Monitoring and metrics
# prometheus-client>=0.17.0
# psutil>=5.9.0
