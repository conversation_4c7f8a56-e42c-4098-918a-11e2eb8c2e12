# A0 Memory Manager - Documentazione Completa

## 🧠 Panoramica

L'agente A0 Memory Manager è il cuore del sistema di gestione della memoria e condivisione delle informazioni nel sistema Heka. Funziona come un database centralizzato e intelligente che memorizza, organizza e condivide tutte le informazioni raccolte dagli altri agenti durante le sessioni di penetration testing.

## 🎯 Obiettivi Principali

1. **Memoria Centralizzata**: Punto unico per tutte le informazioni del sistema
2. **Persistenza**: Le informazioni sopravvivono ai restart del sistema
3. **Condivisione Intelligente**: Correlazioni automatiche tra informazioni
4. **Performance**: Cache in memoria per accesso rapido
5. **Manutenzione**: Cleanup automatico e backup periodici

## 🏗️ Architettura

### Componenti Principali

```
A0 Memory Manager
├── Database SQLite (persistenza)
├── Cache in Memoria (performance)
├── Knowledge Graph (correlazioni)
├── Sistema di Indici (ricerca rapida)
├── Backup Automatico (sicurezza)
└── API Unificata (interfaccia)
```

### Flusso di Informazioni

```
Agente X → A0 Store → Database + Cache + Indici
                ↓
Agente Y ← A0 Retrieve ← Knowledge Graph + Correlazioni
```

## 📊 Tipi di Informazioni Gestite

| Tipo | Descrizione | Esempio |
|------|-------------|---------|
| `TARGET_INFO` | Informazioni sui target | IP, hostname, OS, porte |
| `VULNERABILITY` | Vulnerabilità identificate | CVE, CVSS, exploit |
| `COMMAND_RESULT` | Risultati comandi | Output nmap, gobuster |
| `WEB_RESEARCH` | Ricerca web | Google dorking, OSINT |
| `GENERATED_CODE` | Codice generato | Script, exploit, payload |
| `EXECUTION_LOG` | Log di esecuzione | Comandi eseguiti, errori |
| `ANALYSIS_RESULT` | Risultati analisi | Pattern, correlazioni |
| `REPORT_DATA` | Dati per report | Riassunti, statistiche |
| `AGENT_STATE` | Stati degli agenti | Status, progress |
| `CONTEXT_DATA` | Dati di contesto | Configurazioni, metadata |

## 🔧 API Principale

### Memorizzazione

```python
entry_id = await a0.store_information(
    source_agent="a2_web_researcher",
    info_type=InformationType.WEB_RESEARCH,
    content={
        "target": "example.com",
        "subdomains": ["www", "mail", "ftp"],
        "technologies": ["Apache", "PHP", "MySQL"]
    },
    priority=InformationPriority.HIGH,
    tags={"example.com", "reconnaissance", "web"},
    metadata={"scan_date": "2024-01-15", "tool": "subfinder"}
)
```

### Recupero

```python
# Recupera per target
entries = await a0.retrieve_information(
    requester_agent="a3_command_generator",
    tags={"example.com"}
)

# Recupera per tipo
vulns = await a0.retrieve_information(
    requester_agent="a6_task_monitor",
    info_type=InformationType.VULNERABILITY
)

# Recupera informazioni correlate
related = await a0.get_related_information(entry_id)
```

### Aggiornamento

```python
success = await a0.update_information(
    entry_id="abc123",
    updates={"verified": True, "exploited": True},
    updater_agent="a5_terminal_executor"
)
```

## 🔄 Integrazione con Altri Agenti

### Flusso Automatico

1. **A1** genera prompt → A0 memorizza contesto
2. **A2** ricerca web → A0 memorizza risultati + correla con target esistenti
3. **A3** genera comandi → A0 fornisce contesto da A2 + memorizza comandi
4. **A4** genera codice → A0 fornisce contesto da A2+A3 + memorizza script
5. **A5** esegue comandi → A0 memorizza output + correla con comandi A3
6. **A6** monitora task → A0 fornisce vista completa + memorizza analisi
7. **A7** genera report → A0 fornisce tutti i dati correlati

### Messaggi Automatici

A0 invia automaticamente notifiche quando:
- Nuove informazioni critiche sono disponibili
- Correlazioni interessanti vengono scoperte
- Pattern di attacco vengono identificati

## 📈 Knowledge Graph

### Correlazioni Automatiche

A0 identifica automaticamente correlazioni basate su:

- **Tag Comuni**: Entries con 2+ tag in comune
- **Target Correlati**: Stesso IP, dominio, subnet
- **Temporal Proximity**: Eventi vicini nel tempo
- **Agent Workflow**: Sequenza logica di agenti

### Esempio di Correlazione

```
Target: *************
├── A2: Subdomain scan → mail.example.com
├── A3: Port scan → 25/SMTP, 993/IMAPS  
├── A4: Email enum script
├── A5: Script execution → user list
└── A6: Analysis → email attack vector
```

## 🛠️ Configurazione

### Parametri Principali

```python
class A0MemoryManager:
    def __init__(self):
        # Database
        self.db_path = "data/heka_memory.db"
        
        # Cache
        self.cache_max_size = 1000
        self.cache_ttl = timedelta(hours=1)
        
        # Cleanup
        self.auto_cleanup_interval = timedelta(hours=6)
        self.max_entries_per_agent = 500
        
        # Backup
        self.backup_dir = "data/backups"
```

### Personalizzazione

```python
# Cache più grande per sistemi con più RAM
a0.cache_max_size = 5000

# Backup più frequenti per ambienti critici  
a0.auto_cleanup_interval = timedelta(hours=2)

# Retention più lunga per analisi forensi
a0.max_entries_per_agent = 2000
```

## 📊 Monitoraggio e Statistiche

### Statistiche Disponibili

```python
summary = await a0.get_memory_summary()

print(f"Totale entries: {summary['statistics']['total_entries']}")
print(f"Cache hit ratio: {summary['cache_efficiency']}")
print(f"Database size: {summary['statistics']['database_size_mb']} MB")
print(f"Uptime: {summary['uptime']}")
```

### Metriche per Tipo

```python
# Entries per tipo di informazione
for info_type, count in summary['statistics']['entries_by_type'].items():
    print(f"{info_type}: {count}")

# Entries per agente
for agent, count in summary['statistics']['entries_by_agent'].items():
    print(f"{agent}: {count}")
```

## 🔒 Sicurezza e Permessi

### Controllo Accessi

- Solo l'agente **source** può modificare/eliminare le proprie entries
- Tutti gli agenti possono leggere informazioni pubbliche
- Informazioni sensibili possono essere limitate a agenti specifici

### Audit Trail

Ogni operazione viene tracciata con:
- Timestamp preciso
- Agente richiedente
- Tipo di operazione
- Dati modificati

## 🚀 Best Practices

### Per Sviluppatori

1. **Tag Consistenti**: Usa tag standardizzati (target, tool, tipo)
2. **Metadata Ricchi**: Includi sempre timestamp, tool, confidence
3. **Priorità Appropriate**: CRITICAL solo per vulnerabilità sfruttabili
4. **Cleanup Regolare**: Imposta scadenze per dati temporanei

### Per Amministratori

1. **Backup Regolari**: Verifica che i backup automatici funzionino
2. **Monitoraggio Spazio**: Controlla crescita database
3. **Performance Cache**: Monitora hit ratio cache
4. **Log Analysis**: Analizza log per pattern anomali

## 🐛 Troubleshooting

### Problemi Comuni

**Database Locked**
```bash
# Verifica processi che usano il database
lsof data/heka_memory.db
```

**Cache Miss Elevati**
```python
# Aumenta dimensione cache
a0.cache_max_size = 2000
```

**Spazio Disco Pieno**
```python
# Forza cleanup
await a0._cleanup_expired_entries()
```

### Debug Mode

```python
import logging
logging.getLogger("heka.agent.a0_memory_manager").setLevel(logging.DEBUG)
```

## 📚 Esempi Pratici

Vedi `examples/a0_memory_example.py` per esempi completi di utilizzo.

## 🔮 Roadmap Future

- [ ] Encryption at rest per dati sensibili
- [ ] Replicazione multi-nodo
- [ ] Machine learning per correlazioni avanzate
- [ ] API REST per accesso esterno
- [ ] Dashboard web per visualizzazione
