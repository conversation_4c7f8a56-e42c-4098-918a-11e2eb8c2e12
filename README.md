# Heka Collaborative Agent System

Sistema di 8 agenti collaborativi per penetration testing automatizzato, ispirato al progetto agent-zero.

## 🎯 Panoramica

Heka è un sistema di agenti AI collaborativi progettato per automatizzare le attività di penetration testing. Il sistema è composto da 8 agenti specializzati che lavorano insieme per raggiungere obiettivi di sicurezza informatica.

### 🤖 Gli 8 Agenti

0. **A0 - Memory Manager & Knowledge Base**
   - Gestisce memoria centralizzata e condivisione informazioni
   - Database SQLite per persistenza delle informazioni
   - Knowledge graph per correlazioni tra dati
   - Cache intelligente per accesso rapido
   - Backup automatico e cleanup periodico

1. **A1 - Prompt Generator & LLM Interface**
   - Unico agente autorizzato a comunicare con LLM
   - Gestisce la generazione di prompt e le richieste AI
   - Rate limiting per evitare sovraccarico API

2. **A2 - Web Researcher & Navigator**
   - Ricerca web automatizzata con Playwright
   - Google dorking per reconnaissance
   - Identificazione tecnologie e vulnerabilità web

3. **A3 - Command Generator**
   - Genera comandi Kali Linux appropriati
   - Database completo di tool di penetration testing
   - Prioritizzazione e categorizzazione comandi

4. **A4 - Code Generator**
   - Genera script bash, python e altri linguaggi
   - Automation e exploit development
   - Template per diverse tipologie di codice

5. **A5 - Terminal Executor**
   - Esecuzione sicura di comandi su Kali Linux
   - Gestione timeout e processi
   - Cattura e parsing output

6. **A6 - Task Completion Monitor**
   - Monitora completamento obiettivi
   - Gestisce cicli di esecuzione
   - Analisi progresso e raccomandazioni

7. **A7 - PDF Report Generator**
   - Genera report professionali in PDF/Markdown
   - Analisi vulnerabilità e raccomandazioni
   - Documentazione completa del test

## 🚀 Installazione

### Prerequisiti

- **Sistema Operativo**: Kali Linux (raccomandato)
- **Python**: 3.8+
- **Tool**: nmap, gobuster, nikto, etc. (inclusi in Kali)

### Installazione Dipendenze

```bash
# Clona il repository
git clone <repository-url>
cd heka

# Installa dipendenze Python
pip install -r requirements.txt

# Installa Playwright (per A2)
playwright install --with-deps

# Installa pandoc per PDF (opzionale)
sudo apt-get install pandoc texlive-xetex
```

### Configurazione

1. **Configura AI Client**: Modifica `agents/ai_client.py` con le tue credenziali API
2. **Verifica Tool**: Assicurati che i tool di Kali siano installati
3. **Permessi**: Alcuni tool richiedono privilegi elevati

## 📖 Utilizzo

### 🎮 Modalità Interattiva (Raccomandato)

```bash
# Avvia modalità interattiva con menu colorato
python main.py
```

**Caratteristiche della modalità interattiva:**
- 🎮 **Menu principale** con opzioni chiare e colorate
- ⚙️ **Configurazione guidata** del test step-by-step
- 🎯 **Obiettivi predefiniti** per diversi scenari di testing
- 📊 **Output colorato** e ben organizzato per sezioni
- ⚡ **Output A5 in tempo reale** durante l'esecuzione comandi
- 📋 **Informazioni sistema** dettagliate sugli agenti
- 🔄 **Menu persistente** per test multipli

### 💻 Modalità Command Line

```bash
# Test base
python main.py example.com

# Test con obiettivo specifico
python main.py example.com --objective "Vulnerability assessment completo"

# Strategia sequenziale
python main.py example.com --strategy sequential

# Output verboso con A5 live
python main.py example.com --verbose --live-output

# Mostra tutte le opzioni
python main.py --help
```

**Opzioni disponibili:**
- `--objective, -o`: Obiettivo del penetration test
- `--strategy, -s`: Strategia di coordinamento (`sequential`, `parallel`, `adaptive`)
- `--verbose, -v`: Output verboso con log dettagliati
- `--live-output, -l`: Output A5 in tempo reale durante esecuzione comandi

### 🎯 Esempi di Utilizzo

```bash
# Reconnaissance completo con output live
python main.py target.com --objective "Reconnaissance e information gathering" --live-output

# Vulnerability assessment verboso
python main.py ************* --objective "Vulnerability assessment di rete" --verbose

# Penetration test completo con strategia adattiva
python main.py target.com --objective "Penetration test completo" --strategy adaptive --live-output

# Web application testing
python main.py webapp.example.com --objective "Test sicurezza applicazione web"
```

## 🎨 Nuove Funzionalità v1.0

### 🎮 Menu Interattivo Migliorato
- **Banner ASCII** colorato di benvenuto
- **Menu principale** con 5 opzioni chiare
- **Configurazione guidata** step-by-step
- **Obiettivi predefiniti** per diversi scenari
- **Strategie di esecuzione** selezionabili
- **Opzioni avanzate** configurabili

### 🌈 Output Colorato e Organizzato
- **Sezioni colorate** per diversi tipi di informazioni
- **Icone emoji** per status e messaggi
- **Separatori visivi** per migliorare la leggibilità
- **Codici colore** per successo, errore, warning, info
- **Formattazione consistente** in tutto il sistema

### ⚡ Output A5 in Tempo Reale
- **Streaming live** dell'output dei comandi durante l'esecuzione
- **Callback asincrono** per aggiornamenti in tempo reale
- **Separazione stdout/stderr** con etichette colorate
- **Monitoraggio continuo** senza bloccare l'interfaccia
- **Esecuzione sincrona** - ogni comando attende il completamento prima del successivo

### 🔍 Integrazione Shodan
- **Ricerca avanzata** su Shodan per intelligence tecnica
- **14 query specializzate** per diversi servizi e porte
- **Sostituzione Google** con motori più privacy-friendly
- **Ricerca hostname/IP** per informazioni dettagliate sui target
- **Analisi servizi** esposti e vulnerabilità potenziali

### 📊 Miglioramenti UX
- **Riassunto risultati** colorato e strutturato
- **Statistiche esecuzione** con tempi e status
- **Informazioni sistema** dettagliate sugli agenti
- **Gestione errori** migliorata con messaggi chiari

## 🏗️ Architettura

### Flusso di Esecuzione

```
A1 (Prompt) → A2 (Web Research) → A3 (Commands) → A4 (Scripts)
                                        ↓
A7 (Report) ← A6 (Monitor) ← A5 (Execute)
```

### Strategie di Coordinamento

- **Sequential**: Agenti eseguono in sequenza rigorosa
- **Parallel**: Alcuni agenti eseguono in parallelo
- **Adaptive**: Strategia adattiva basata sul task (default)

### Comunicazione tra Agenti

- Sistema di messaggi asincroni
- Coordinatore centrale per routing
- Dipendenze e prerequisiti gestiti automaticamente

## 🔍 Motori di Ricerca e Intelligence

### Agente A2 - Motori Supportati

**A2 utilizza i seguenti motori per la ricerca:**
- **🦆 DuckDuckGo**: Ricerca privacy-friendly senza tracking
- **🔍 Bing**: Motore di ricerca Microsoft per risultati completi
- **🌐 Shodan**: Motore specializzato per dispositivi IoT e servizi esposti
- **🔒 crt.sh**: Database certificati SSL/TLS per Certificate Transparency
- **🕵️ IntelX**: Motore di ricerca per intelligence e OSINT
- **👁️ GreyNoise**: Intelligence su IP e attività di scansione
- **📚 Wayback Machine**: Archivio storico di pagine web

### Query Shodan Specializzate

**Ricerche per target:**
- `hostname:target` - Ricerca per hostname specifico
- `ip:target` - Ricerca per indirizzo IP

**Ricerche per porte:**
- `port:22 target` - SSH services
- `port:80 target` - HTTP services
- `port:443 target` - HTTPS services
- `port:21 target` - FTP services
- `port:23 target` - Telnet services
- `port:3389 target` - RDP services

**Ricerche per servizi:**
- `apache target` - Server Apache
- `nginx target` - Server Nginx
- `iis target` - Server IIS
- `ssh target` - Servizi SSH
- `ftp target` - Servizi FTP
- `telnet target` - Servizi Telnet

### Query Specializzate per Altri Servizi

**Certificate Transparency (crt.sh):**
- `target` - Certificati per dominio esatto
- `%.target` - Certificati per sottodomini
- `www.target`, `mail.target`, `api.target` - Servizi specifici

**Intelligence X:**
- `target` - Ricerca generale
- `domain:target` - Ricerca per dominio
- `email:target` - Ricerca email associate
- `ip:target` - Ricerca per IP

**GreyNoise (solo IP):**
- Analisi automatica dell'IP per attività di scansione e minacce

**Wayback Machine:**
- `target` - Archivio del dominio
- `http://target`, `https://target` - Protocolli specifici
- `www.target` - Versione www del sito

## 📁 Struttura del Progetto

```
heka/
├── agents/
│   ├── core/
│   │   ├── base_agent.py          # Classe base agenti
│   │   └── agent_coordinator.py   # Coordinatore centrale
│   ├── a0_memory_manager.py       # Agente A0 - Memory Manager
│   ├── a1_prompt_generator.py     # Agente A1
│   ├── a2_web_researcher.py       # Agente A2
│   ├── a3_command_generator.py    # Agente A3
│   ├── a4_code_generator.py       # Agente A4
│   ├── a5_terminal_executor.py    # Agente A5
│   ├── a6_task_monitor.py         # Agente A6
│   ├── a7_report_generator.py     # Agente A7
│   └── ai_client.py               # Client LLM
├── data/                          # Directory dati A0
│   ├── heka_memory.db            # Database SQLite memoria
│   └── backups/                  # Backup automatici
├── main.py                        # Entry point principale
├── requirements.txt               # Dipendenze Python
└── README.md                      # Questa documentazione
```

## 🧠 Memory Manager A0

### Funzionalità Principali

- **Memoria Centralizzata**: Database SQLite per persistenza di tutte le informazioni
- **Cache Intelligente**: Sistema di cache in memoria con eviction automatica
- **Knowledge Graph**: Correlazioni automatiche tra informazioni correlate
- **Backup Automatico**: Backup periodici con retention policy
- **API Unificata**: Interfaccia semplice per store/retrieve informazioni
- **Cleanup Automatico**: Rimozione automatica di informazioni scadute

### Tipi di Informazioni Gestite

- **TARGET_INFO**: Informazioni sui target
- **VULNERABILITY**: Vulnerabilità identificate
- **COMMAND_RESULT**: Risultati esecuzione comandi
- **WEB_RESEARCH**: Risultati ricerca web
- **GENERATED_CODE**: Codice generato
- **EXECUTION_LOG**: Log di esecuzione
- **ANALYSIS_RESULT**: Risultati analisi
- **REPORT_DATA**: Dati per report
- **AGENT_STATE**: Stati degli agenti
- **CONTEXT_DATA**: Dati di contesto

### Integrazione con Altri Agenti

A0 si integra automaticamente con tutti gli altri agenti:

1. **Memorizzazione Automatica**: Ogni risultato viene automaticamente memorizzato
2. **Condivisione Contestuale**: Informazioni correlate vengono condivise automaticamente
3. **Persistenza**: Tutte le informazioni sopravvivono ai restart del sistema
4. **Correlazioni**: Il knowledge graph identifica automaticamente relazioni

## 🔧 Configurazione Avanzata

### Personalizzazione Agenti

Ogni agente può essere configurato modificando i parametri nel costruttore:

```python
# Esempio: Timeout personalizzato per A5
class A5TerminalExecutor(BaseAgent):
    def __init__(self):
        super().__init__(...)
        self.default_timeout = 600  # 10 minuti
```

### Aggiunta Nuovi Tool

Per aggiungere nuovi tool a A3:

```python
# In a3_command_generator.py
self.command_templates["new_category"] = {
    "new_tool": {
        "command": "newtool {target}",
        "description": "Descrizione tool",
        "priority": 5
    }
}
```

### Personalizzazione Report

Modifica i template in A7 per personalizzare i report:

```python
# In a7_report_generator.py
self.report_template["new_section"] = {
    "title": "Nuova Sezione",
    "description": "Descrizione sezione"
}
```

## 🛡️ Sicurezza

### Comandi Pericolosi

A5 include protezioni contro comandi pericolosi:
- `rm -rf /`
- `dd if=`
- `format`
- Altri comandi distruttivi

### Limitazioni di Rete

- Test limitati al target specificato
- Nessun attacco a infrastrutture critiche
- Rispetto delle policy di sicurezza

### Rate Limiting

- A1 implementa rate limiting per API LLM
- Prevenzione sovraccarico servizi target
- Gestione timeout appropriati

## 📊 Monitoraggio e Logging

### Log Files

- `heka.log`: Log principale del sistema
- `/tmp/heka_execution/`: Output comandi
- `/tmp/heka_reports/`: Report generati

### Metriche

Ogni agente traccia:
- Tempo di esecuzione
- Task completati/falliti
- Statistiche specifiche

### Debug

```bash
# Abilita debug logging
python main.py target.com --verbose

# Log specifico agente
export HEKA_LOG_LEVEL=DEBUG
```

## 🤝 Contribuire

### Sviluppo

1. Fork del repository
2. Crea branch feature: `git checkout -b feature/nuova-funzionalita`
3. Commit modifiche: `git commit -am 'Aggiunge nuova funzionalità'`
4. Push branch: `git push origin feature/nuova-funzionalita`
5. Crea Pull Request

### Aggiungere Nuovi Agenti

Per aggiungere un nuovo agente:

1. Estendi `BaseAgent`
2. Implementa `execute_task()`
3. Registra nel coordinatore
4. Aggiorna dipendenze

### Testing

```bash
# Test base
python -m pytest tests/

# Test specifico agente
python -m pytest tests/test_a1_prompt_generator.py
```

## 📄 Licenza

Questo progetto è rilasciato sotto licenza MIT. Vedi il file `LICENSE` per dettagli.

## 🙏 Ringraziamenti

- Ispirato dal progetto [agent-zero](https://github.com/agent0ai/agent-zero)
- Community Kali Linux per i tool di penetration testing
- Playwright team per l'automazione web

## 📞 Supporto

Per supporto e domande:
- Apri un issue su GitHub
- Consulta la documentazione
- Controlla i log per errori

---

**⚠️ Disclaimer**: Questo tool è destinato esclusivamente a test di sicurezza autorizzati. L'uso non autorizzato è illegale e non supportato.
