"""
Agent A5 - Terminal Executor
Responsabile dell'esecuzione di comandi nel terminale Kali Linux
"""

import asyncio
import logging
import json
import os
import signal
import subprocess
import tempfile
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta

from .core.base_agent import BaseAgent, AgentStatus, TaskResult, MessageType

class CommandExecution:
    """Rappresenta l'esecuzione di un comando"""
    def __init__(self, command: str, timeout: int = 300):
        self.command = command
        self.timeout = timeout
        self.start_time = None
        self.end_time = None
        self.return_code = None
        self.stdout = ""
        self.stderr = ""
        self.process = None
        self.execution_id = None

class A5TerminalExecutor(BaseAgent):
    """Agent A5 - Esecutore di comandi terminale"""
    
    def __init__(self):
        super().__init__(
            agent_id="a5_terminal_executor",
            name="Terminal Executor",
            description="Esegue comandi nel terminale Kali Linux per penetration testing"
        )
        
        # Configurazione esecuzione
        self.max_concurrent_commands = 3
        self.default_timeout = 300  # 5 minuti
        self.max_timeout = 1800     # 30 minuti
        
        # Comandi in esecuzione
        self.running_commands: Dict[str, CommandExecution] = {}
        self.completed_commands: List[CommandExecution] = []
        self.command_counter = 0

        # Callback per output in tempo reale
        self.output_callback = None
        
        # Comandi pericolosi da evitare
        self.dangerous_commands = [
            "rm -rf /", "dd if=", "mkfs", "format", "fdisk", "parted",
            "shutdown", "reboot", "halt", "poweroff", "init 0", "init 6",
            ":(){ :|:& };:", "chmod -R 777 /", "chown -R root:root /"
        ]
        
        # Comandi che richiedono privilegi elevati
        self.privileged_commands = [
            "nmap", "masscan", "tcpdump", "wireshark", "aircrack-ng",
            "john", "hashcat", "hydra", "metasploit", "msfconsole"
        ]
        
        # Directory di lavoro
        self.work_dir = "/tmp/heka_execution"
        self.output_dir = None

        # Directory per script temporanei
        self.script_dir = "/tmp/heka_scripts"
        os.makedirs(self.script_dir, exist_ok=True)

    def set_output_callback(self, callback):
        """Imposta callback per output in tempo reale"""
        self.output_callback = callback
        
    async def initialize(self):
        """Inizializza l'agente A5"""
        try:
            # Crea directory di lavoro
            os.makedirs(self.work_dir, exist_ok=True)
            
            # Crea directory per output
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.output_dir = f"{self.work_dir}/session_{timestamp}"
            os.makedirs(self.output_dir, exist_ok=True)
            
            # Verifica che siamo su Kali Linux
            await self._verify_kali_environment()
            
            # Verifica tool disponibili
            await self._check_available_tools()
            
            self.logger.info("Agent A5 inizializzato")
            
        except Exception as e:
            self.logger.error(f"Errore inizializzazione A5: {e}")
            raise
    
    async def shutdown(self):
        """Chiude l'agente A5"""
        # Termina tutti i comandi in esecuzione
        for execution_id, cmd_exec in self.running_commands.items():
            if cmd_exec.process and cmd_exec.process.poll() is None:
                try:
                    cmd_exec.process.terminate()
                    await asyncio.sleep(2)
                    if cmd_exec.process.poll() is None:
                        cmd_exec.process.kill()
                except:
                    pass
        
        self.logger.info("Agent A5 chiuso")
    
    async def execute_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """Esegue un task di esecuzione comandi"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self.update_status(AgentStatus.WORKING)
            
            objective = task_data.get("objective", "")
            target = task_data.get("target", "")
            shared_context = task_data.get("shared_context", {})
            previous_results = task_data.get("previous_results", {})
            
            self.logger.info(f"A5 - Esecuzione comandi per target: {target}")
            
            # Ottieni comandi da A3
            commands_data = previous_results.get("a3_command_generator", {})
            commands = commands_data.get("commands_generated", [])
            
            # Ottieni script da A4
            scripts_data = previous_results.get("a4_code_generator", {})
            scripts = scripts_data.get("scripts_generated", [])
            
            # Esegui comandi in ordine di priorità
            execution_results = await self._execute_commands_sequence(commands, target)
            
            # Esegui script generati
            script_results = await self._execute_generated_scripts(scripts, target)
            
            # Combina risultati
            all_results = execution_results + script_results
            
            execution_time = asyncio.get_event_loop().time() - start_time
            self.total_execution_time += execution_time
            self.tasks_completed += 1
            
            result_data = {
                "commands_executed": len(execution_results),
                "scripts_executed": len(script_results),
                "successful_executions": len([r for r in all_results if r["success"]]),
                "failed_executions": len([r for r in all_results if not r["success"]]),
                "execution_results": all_results,
                "output_directory": self.output_dir,
                "execution_time": execution_time
            }
            
            await self.update_status(AgentStatus.IDLE)
            
            return TaskResult(
                success=True,
                data=result_data,
                execution_time=execution_time
            )
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione task A5: {e}")
            self.tasks_failed += 1
            await self.update_status(AgentStatus.ERROR)
            
            return TaskResult(
                success=False,
                error=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def _verify_kali_environment(self):
        """Verifica che siamo in ambiente Kali Linux"""
        try:
            # Controlla /etc/os-release
            if os.path.exists("/etc/os-release"):
                with open("/etc/os-release", "r") as f:
                    content = f.read()
                    if "kali" not in content.lower():
                        self.logger.warning("Non sembra essere un sistema Kali Linux")
            
            # Controlla presenza directory tipiche di Kali
            kali_dirs = ["/usr/share/wordlists", "/usr/share/metasploit-framework", "/usr/share/nmap"]
            for directory in kali_dirs:
                if not os.path.exists(directory):
                    self.logger.warning(f"Directory Kali non trovata: {directory}")
            
        except Exception as e:
            self.logger.warning(f"Errore verifica ambiente Kali: {e}")
    
    async def _check_available_tools(self):
        """Verifica disponibilità dei tool di penetration testing"""
        tools_to_check = [
            "nmap", "masscan", "gobuster", "dirb", "nikto", "sqlmap",
            "hydra", "john", "hashcat", "metasploit-framework", "whatweb",
            "subfinder", "amass", "nuclei", "wpscan"
        ]
        
        available_tools = []
        missing_tools = []
        
        for tool in tools_to_check:
            try:
                result = subprocess.run(["which", tool], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    available_tools.append(tool)
                else:
                    missing_tools.append(tool)
            except:
                missing_tools.append(tool)
        
        self.logger.info(f"Tool disponibili: {len(available_tools)}")
        if missing_tools:
            self.logger.warning(f"Tool mancanti: {missing_tools}")
    
    async def _execute_commands_sequence(self, commands: List[Dict[str, Any]], target: str) -> List[Dict[str, Any]]:
        """Esegue una sequenza di comandi"""
        results = []
        
        for i, cmd_info in enumerate(commands):
            command = cmd_info.get("command", "")
            category = cmd_info.get("category", "unknown")
            priority = cmd_info.get("priority", 5)
            
            self.logger.info(f"Esecuzione comando {i+1}/{len(commands)}: {command[:50]}...")
            
            # Verifica sicurezza comando
            if not self._is_command_safe(command):
                self.logger.warning(f"Comando considerato pericoloso, saltato: {command}")
                results.append({
                    "command": command,
                    "success": False,
                    "error": "Comando considerato pericoloso",
                    "category": category,
                    "skipped": True
                })
                continue
            
            # Esegui comando
            execution_result = await self._execute_single_command(command, category, target)
            results.append(execution_result)
            
            # Pausa tra comandi per evitare sovraccarico
            if i < len(commands) - 1:
                await asyncio.sleep(2)
        
        return results
    
    async def _execute_generated_scripts(self, scripts: List[Dict[str, Any]], target: str) -> List[Dict[str, Any]]:
        """Esegue script generati da A4"""
        results = []
        
        for script_info in scripts:
            script_path = script_info.get("path", "")
            script_name = script_info.get("name", "")
            script_type = script_info.get("type", "unknown")
            
            if not os.path.exists(script_path):
                self.logger.warning(f"Script non trovato: {script_path}")
                continue
            
            self.logger.info(f"Esecuzione script: {script_name}")
            
            # Determina comando di esecuzione basato sul tipo di script
            if script_path.endswith(".py"):
                command = f"python3 {script_path}"
            elif script_path.endswith(".sh"):
                command = f"bash {script_path}"
            else:
                command = script_path
            
            # Esegui script e attendi completamento
            execution_result = await self._execute_single_command(command, script_type, target)
            execution_result["script_name"] = script_name
            execution_result["script_type"] = script_type
            results.append(execution_result)

            # Pausa tra script per permettere completamento
            await asyncio.sleep(1)
        
        return results
    
    async def _execute_single_command(self, command: str, category: str, target: str) -> Dict[str, Any]:
        """Esegue un singolo comando"""
        self.command_counter += 1
        execution_id = f"cmd_{self.command_counter}_{datetime.now().strftime('%H%M%S')}"
        
        # Crea oggetto esecuzione
        cmd_exec = CommandExecution(command, self.default_timeout)
        cmd_exec.execution_id = execution_id
        cmd_exec.start_time = datetime.now()
        
        # Prepara file di output
        output_file = f"{self.output_dir}/{execution_id}.txt"
        error_file = f"{self.output_dir}/{execution_id}_error.txt"
        
        try:
            self.logger.debug(f"Esecuzione: {command}")

            # Notifica inizio comando al callback
            if self.output_callback:
                try:
                    self.output_callback(f"[COMANDO AVVIATO] {command}")
                except Exception as e:
                    self.logger.debug(f"Errore callback inizio: {e}")

            # Avvia processo
            cmd_exec.process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.work_dir,
                preexec_fn=os.setsid  # Crea nuovo process group
            )

            self.running_commands[execution_id] = cmd_exec
            
            # Attendi completamento con timeout
            try:
                stdout, stderr = await asyncio.wait_for(
                    self._communicate_with_process_live(cmd_exec.process),
                    timeout=cmd_exec.timeout
                )

                cmd_exec.stdout = stdout
                cmd_exec.stderr = stderr
                cmd_exec.return_code = cmd_exec.process.returncode
                
            except asyncio.TimeoutError:
                self.logger.warning(f"Comando timeout dopo {cmd_exec.timeout}s: {command}")
                
                # Termina processo
                try:
                    os.killpg(os.getpgid(cmd_exec.process.pid), signal.SIGTERM)
                    await asyncio.sleep(5)
                    if cmd_exec.process.poll() is None:
                        os.killpg(os.getpgid(cmd_exec.process.pid), signal.SIGKILL)
                except:
                    pass
                
                cmd_exec.return_code = -1
                cmd_exec.stderr = f"Comando terminato per timeout ({cmd_exec.timeout}s)"
            
            cmd_exec.end_time = datetime.now()
            
            # Salva output su file
            if cmd_exec.stdout:
                with open(output_file, 'w') as f:
                    f.write(cmd_exec.stdout)
            
            if cmd_exec.stderr:
                with open(error_file, 'w') as f:
                    f.write(cmd_exec.stderr)
            
            # Rimuovi da comandi in esecuzione
            if execution_id in self.running_commands:
                del self.running_commands[execution_id]
            
            self.completed_commands.append(cmd_exec)
            
            # Prepara risultato
            success = cmd_exec.return_code == 0
            execution_time = (cmd_exec.end_time - cmd_exec.start_time).total_seconds()
            
            result = {
                "command": command,
                "execution_id": execution_id,
                "success": success,
                "return_code": cmd_exec.return_code,
                "execution_time": execution_time,
                "category": category,
                "output_file": output_file if cmd_exec.stdout else None,
                "error_file": error_file if cmd_exec.stderr else None,
                "stdout_preview": cmd_exec.stdout[:500] if cmd_exec.stdout else "",
                "stderr_preview": cmd_exec.stderr[:500] if cmd_exec.stderr else ""
            }
            
            if not success:
                result["error"] = cmd_exec.stderr or f"Comando fallito con codice {cmd_exec.return_code}"
            
            self.logger.info(f"Comando completato: {success}, tempo: {execution_time:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione comando: {e}")
            
            # Cleanup
            if execution_id in self.running_commands:
                del self.running_commands[execution_id]
            
            return {
                "command": command,
                "execution_id": execution_id,
                "success": False,
                "error": str(e),
                "category": category,
                "execution_time": 0
            }
    
    async def _communicate_with_process(self, process):
        """Comunica con il processo in modo asincrono"""
        loop = asyncio.get_event_loop()

        # Esegui communicate in thread separato
        stdout, stderr = await loop.run_in_executor(
            None, process.communicate
        )

        return stdout, stderr

    async def _communicate_with_process_live(self, process):
        """Comunica con il processo con output in tempo reale e attende completamento"""
        stdout_lines = []
        stderr_lines = []
        process_finished = False

        # Leggi stdout in tempo reale
        async def read_stdout():
            nonlocal process_finished
            while not process_finished:
                try:
                    line = await asyncio.get_event_loop().run_in_executor(
                        None, process.stdout.readline
                    )
                    if not line:
                        break
                    line = line.strip()
                    if line:
                        stdout_lines.append(line)
                        # Invia output al callback se disponibile
                        if self.output_callback:
                            try:
                                self.output_callback(line)
                            except Exception as e:
                                self.logger.debug(f"Errore callback output: {e}")
                except Exception as e:
                    self.logger.debug(f"Errore lettura stdout: {e}")
                    break

        # Leggi stderr in tempo reale
        async def read_stderr():
            nonlocal process_finished
            while not process_finished:
                try:
                    line = await asyncio.get_event_loop().run_in_executor(
                        None, process.stderr.readline
                    )
                    if not line:
                        break
                    line = line.strip()
                    if line:
                        stderr_lines.append(line)
                        # Invia anche stderr al callback
                        if self.output_callback:
                            try:
                                self.output_callback(f"[STDERR] {line}")
                            except Exception as e:
                                self.logger.debug(f"Errore callback stderr: {e}")
                except Exception as e:
                    self.logger.debug(f"Errore lettura stderr: {e}")
                    break

        # Avvia lettura asincrona
        stdout_task = asyncio.create_task(read_stdout())
        stderr_task = asyncio.create_task(read_stderr())

        try:
            # Attendi completamento processo
            await asyncio.get_event_loop().run_in_executor(None, process.wait)

            # Segna che il processo è finito
            process_finished = True

            # Invia notifica di completamento al callback
            if self.output_callback:
                try:
                    self.output_callback(f"[PROCESSO COMPLETATO] Exit code: {process.returncode}")
                except Exception as e:
                    self.logger.debug(f"Errore callback completamento: {e}")

            # Attendi un po' per permettere ai task di leggere gli ultimi output
            await asyncio.sleep(0.5)

        finally:
            # Cancella task di lettura
            stdout_task.cancel()
            stderr_task.cancel()

            # Attendi che i task finiscano
            try:
                await stdout_task
            except asyncio.CancelledError:
                pass
            try:
                await stderr_task
            except asyncio.CancelledError:
                pass

        return '\n'.join(stdout_lines), '\n'.join(stderr_lines)
    
    def _is_command_safe(self, command: str) -> bool:
        """Verifica se un comando è sicuro da eseguire"""
        command_lower = command.lower()
        
        # Controlla comandi pericolosi
        for dangerous in self.dangerous_commands:
            if dangerous in command_lower:
                return False
        
        # Controlla pattern pericolosi
        dangerous_patterns = [
            r"rm\s+-rf\s+/",
            r"dd\s+if=.*of=/dev/",
            r"mkfs\.",
            r"format\s+",
            r">\s*/dev/sd[a-z]",
            r"cat\s+.*>\s*/etc/"
        ]
        
        import re
        for pattern in dangerous_patterns:
            if re.search(pattern, command_lower):
                return False
        
        return True
    
    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche specifiche di A5"""
        base_stats = super().get_stats()
        
        a5_stats = {
            "commands_executed_total": len(self.completed_commands),
            "commands_running": len(self.running_commands),
            "successful_commands": len([cmd for cmd in self.completed_commands if cmd.return_code == 0]),
            "failed_commands": len([cmd for cmd in self.completed_commands if cmd.return_code != 0]),
            "output_directory": self.output_dir,
            "work_directory": self.work_dir
        }
        
        base_stats.update(a5_stats)
        return base_stats
