"""
Agent A7 - PDF Report Generator
Responsabile della generazione di report PDF professionali per penetration testing
"""

import asyncio
import logging
import json
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

from .core.base_agent import BaseAgent, AgentStatus, TaskResult, MessageType

class ReportSection:
    """Rappresenta una sezione del report"""
    def __init__(self, title: str, content: str, level: int = 1):
        self.title = title
        self.content = content
        self.level = level
        self.subsections = []

class A7ReportGenerator(BaseAgent):
    """Agent A7 - Generatore di report PDF"""
    
    def __init__(self):
        super().__init__(
            agent_id="a7_report_generator",
            name="PDF Report Generator",
            description="Genera report PDF professionali per penetration testing"
        )
        
        # Template del report
        self.report_template = {
            "executive_summary": {
                "title": "Executive Summary",
                "description": "Riassunto esecutivo dei risultati"
            },
            "methodology": {
                "title": "Methodology",
                "description": "Metodologia utilizzata per il test"
            },
            "scope": {
                "title": "Scope and Limitations",
                "description": "Ambito e limitazioni del test"
            },
            "findings": {
                "title": "Findings",
                "description": "Risultati dettagliati del test"
            },
            "vulnerabilities": {
                "title": "Vulnerabilities",
                "description": "Vulnerabilità identificate"
            },
            "recommendations": {
                "title": "Recommendations",
                "description": "Raccomandazioni per la remediation"
            },
            "technical_details": {
                "title": "Technical Details",
                "description": "Dettagli tecnici dell'assessment"
            },
            "appendix": {
                "title": "Appendix",
                "description": "Informazioni aggiuntive e raw data"
            }
        }
        
        # Classificazione severità vulnerabilità
        self.severity_levels = {
            "critical": {"color": "#DC2626", "score": 10, "label": "Critical"},
            "high": {"color": "#EA580C", "score": 8, "label": "High"},
            "medium": {"color": "#D97706", "score": 6, "label": "Medium"},
            "low": {"color": "#65A30D", "score": 4, "label": "Low"},
            "info": {"color": "#0891B2", "score": 2, "label": "Informational"}
        }
        
        # Directory per report
        self.reports_dir = "/tmp/heka_reports"
        
    async def initialize(self):
        """Inizializza l'agente A7"""
        try:
            # Crea directory per report
            os.makedirs(self.reports_dir, exist_ok=True)
            
            # Verifica disponibilità tool per PDF
            await self._check_pdf_tools()
            
            self.logger.info("Agent A7 inizializzato")
            
        except Exception as e:
            self.logger.error(f"Errore inizializzazione A7: {e}")
            raise
    
    async def shutdown(self):
        """Chiude l'agente A7"""
        self.logger.info("Agent A7 chiuso")
    
    async def execute_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """Esegue un task di generazione report"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self.update_status(AgentStatus.WORKING)
            
            objective = task_data.get("objective", "")
            target = task_data.get("target", "")
            shared_context = task_data.get("shared_context", {})
            previous_results = task_data.get("previous_results", {})
            
            self.logger.info(f"A7 - Generazione report per target: {target}")
            
            # Raccogli tutti i dati dai risultati precedenti
            report_data = await self._collect_report_data(
                objective, target, previous_results
            )
            
            # Genera le sezioni del report
            report_sections = await self._generate_report_sections(report_data)
            
            # Genera report in formato Markdown
            markdown_report = await self._generate_markdown_report(
                report_data, report_sections
            )
            
            # Salva report Markdown
            markdown_path = await self._save_markdown_report(
                markdown_report, target
            )
            
            # Genera PDF se possibile
            pdf_path = await self._generate_pdf_report(
                markdown_path, target
            )
            
            # Genera report JSON per dati strutturati
            json_path = await self._save_json_report(
                report_data, target
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            self.total_execution_time += execution_time
            self.tasks_completed += 1
            
            result_data = {
                "report_generated": True,
                "markdown_report": markdown_path,
                "pdf_report": pdf_path,
                "json_report": json_path,
                "vulnerabilities_found": len(report_data.get("vulnerabilities", [])),
                "findings_count": len(report_data.get("findings", [])),
                "execution_time": execution_time
            }
            
            await self.update_status(AgentStatus.IDLE)
            
            return TaskResult(
                success=True,
                data=result_data,
                execution_time=execution_time
            )
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione task A7: {e}")
            self.tasks_failed += 1
            await self.update_status(AgentStatus.ERROR)
            
            return TaskResult(
                success=False,
                error=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def _check_pdf_tools(self):
        """Verifica disponibilità tool per generazione PDF"""
        try:
            # Controlla pandoc
            import subprocess
            result = subprocess.run(["which", "pandoc"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                self.logger.info("Pandoc disponibile per generazione PDF")
            else:
                self.logger.warning("Pandoc non disponibile - solo report Markdown")
                
        except Exception as e:
            self.logger.warning(f"Errore controllo tool PDF: {e}")
    
    async def _collect_report_data(self, objective: str, target: str, 
                                 previous_results: Dict[str, Any]) -> Dict[str, Any]:
        """Raccoglie tutti i dati per il report"""
        report_data = {
            "metadata": {
                "target": target,
                "objective": objective,
                "scan_date": datetime.now().isoformat(),
                "generated_by": "Heka Collaborative Agent System",
                "version": "1.0"
            },
            "scope": {
                "target": target,
                "objective": objective,
                "methodology": "Automated Penetration Testing"
            },
            "findings": [],
            "vulnerabilities": [],
            "technical_details": {},
            "recommendations": [],
            "raw_data": previous_results
        }
        
        # Estrai dati da A2 (Web Research)
        web_research = previous_results.get("a2_web_researcher", {})
        if web_research:
            report_data["technical_details"]["web_research"] = {
                "technologies": web_research.get("technologies_identified", []),
                "subdomains": web_research.get("subdomains", []),
                "vulnerabilities": web_research.get("vulnerabilities_found", [])
            }
            
            # Aggiungi vulnerabilità trovate
            for vuln in web_research.get("vulnerabilities_found", []):
                report_data["vulnerabilities"].append({
                    "title": vuln.get("potential_issue", "Unknown Vulnerability"),
                    "severity": self._classify_vulnerability_severity(vuln),
                    "description": vuln.get("description", ""),
                    "location": vuln.get("url", target),
                    "source": "Web Research",
                    "evidence": vuln.get("evidence", "")
                })
        
        # Estrai dati da A3 (Command Generator)
        commands_data = previous_results.get("a3_command_generator", {})
        if commands_data:
            report_data["technical_details"]["commands_generated"] = {
                "total_commands": len(commands_data.get("commands_generated", [])),
                "categories": commands_data.get("command_categories", [])
            }
        
        # Estrai dati da A4 (Code Generator)
        scripts_data = previous_results.get("a4_code_generator", {})
        if scripts_data:
            report_data["technical_details"]["scripts_generated"] = {
                "total_scripts": scripts_data.get("total_scripts", 0),
                "script_types": scripts_data.get("script_types", []),
                "languages": scripts_data.get("languages_used", [])
            }
        
        # Estrai dati da A5 (Terminal Executor)
        execution_data = previous_results.get("a5_terminal_executor", {})
        if execution_data:
            report_data["technical_details"]["execution_results"] = {
                "commands_executed": execution_data.get("commands_executed", 0),
                "successful_executions": execution_data.get("successful_executions", 0),
                "failed_executions": execution_data.get("failed_executions", 0)
            }
            
            # Analizza risultati per findings
            for result in execution_data.get("execution_results", []):
                if result.get("success") and result.get("stdout_preview"):
                    finding = self._extract_finding_from_output(result)
                    if finding:
                        report_data["findings"].append(finding)
        
        # Estrai dati da A6 (Task Monitor)
        monitor_data = previous_results.get("a6_task_monitor", {})
        if monitor_data:
            report_data["completion_analysis"] = {
                "objective_reached": monitor_data.get("objective_reached", False),
                "completion_score": monitor_data.get("completion_score", 0.0),
                "met_criteria": monitor_data.get("met_criteria", []),
                "recommendations": monitor_data.get("recommendations", [])
            }
            
            # Aggiungi raccomandazioni
            report_data["recommendations"].extend(monitor_data.get("recommendations", []))
        
        return report_data
    
    def _classify_vulnerability_severity(self, vuln: Dict[str, Any]) -> str:
        """Classifica la severità di una vulnerabilità"""
        issue = vuln.get("potential_issue", "").lower()
        
        if any(keyword in issue for keyword in ["sql injection", "remote code execution", "authentication bypass"]):
            return "critical"
        elif any(keyword in issue for keyword in ["xss", "csrf", "directory traversal"]):
            return "high"
        elif any(keyword in issue for keyword in ["information disclosure", "weak encryption"]):
            return "medium"
        elif any(keyword in issue for keyword in ["version disclosure", "banner"]):
            return "low"
        else:
            return "info"
    
    def _extract_finding_from_output(self, result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Estrae finding dall'output di un comando"""
        command = result.get("command", "")
        output = result.get("stdout_preview", "")
        
        if not output:
            return None
        
        # Analizza output per pattern interessanti
        if "nmap" in command.lower():
            return {
                "title": "Port Scan Results",
                "description": f"Port scan executed: {command}",
                "evidence": output[:200],
                "category": "reconnaissance",
                "tool": "nmap"
            }
        elif "gobuster" in command.lower() or "dirb" in command.lower():
            return {
                "title": "Directory Enumeration",
                "description": f"Directory enumeration: {command}",
                "evidence": output[:200],
                "category": "enumeration",
                "tool": "gobuster/dirb"
            }
        elif "nikto" in command.lower():
            return {
                "title": "Web Vulnerability Scan",
                "description": f"Web vulnerability scan: {command}",
                "evidence": output[:200],
                "category": "vulnerability_assessment",
                "tool": "nikto"
            }
        
        return None
    
    async def _generate_report_sections(self, report_data: Dict[str, Any]) -> List[ReportSection]:
        """Genera le sezioni del report"""
        sections = []
        
        # Executive Summary
        exec_summary = self._generate_executive_summary(report_data)
        sections.append(ReportSection("Executive Summary", exec_summary, 1))
        
        # Methodology
        methodology = self._generate_methodology_section(report_data)
        sections.append(ReportSection("Methodology", methodology, 1))
        
        # Scope
        scope = self._generate_scope_section(report_data)
        sections.append(ReportSection("Scope and Limitations", scope, 1))
        
        # Findings
        findings = self._generate_findings_section(report_data)
        sections.append(ReportSection("Findings", findings, 1))
        
        # Vulnerabilities
        vulnerabilities = self._generate_vulnerabilities_section(report_data)
        sections.append(ReportSection("Vulnerabilities", vulnerabilities, 1))
        
        # Recommendations
        recommendations = self._generate_recommendations_section(report_data)
        sections.append(ReportSection("Recommendations", recommendations, 1))
        
        # Technical Details
        technical = self._generate_technical_section(report_data)
        sections.append(ReportSection("Technical Details", technical, 1))
        
        return sections
    
    def _generate_executive_summary(self, report_data: Dict[str, Any]) -> str:
        """Genera executive summary"""
        target = report_data["metadata"]["target"]
        objective = report_data["metadata"]["objective"]
        vuln_count = len(report_data["vulnerabilities"])
        findings_count = len(report_data["findings"])
        
        completion = report_data.get("completion_analysis", {})
        objective_reached = completion.get("objective_reached", False)
        completion_score = completion.get("completion_score", 0.0)
        
        summary = f"""
## Executive Summary

This report presents the results of an automated penetration testing assessment conducted against **{target}** with the objective: *{objective}*.

### Key Results:
- **Target**: {target}
- **Assessment Date**: {report_data["metadata"]["scan_date"][:10]}
- **Objective Completion**: {"✅ Achieved" if objective_reached else "⚠️ Partial"} ({completion_score:.1%})
- **Vulnerabilities Found**: {vuln_count}
- **Technical Findings**: {findings_count}

### Risk Assessment:
"""
        
        # Aggiungi riassunto vulnerabilità per severità
        severity_counts = {}
        for vuln in report_data["vulnerabilities"]:
            severity = vuln.get("severity", "info")
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        for severity in ["critical", "high", "medium", "low", "info"]:
            count = severity_counts.get(severity, 0)
            if count > 0:
                label = self.severity_levels[severity]["label"]
                summary += f"- **{label}**: {count} vulnerabilities\n"
        
        if objective_reached:
            summary += "\n✅ **The assessment objective has been successfully achieved.**"
        else:
            summary += f"\n⚠️ **The assessment is {completion_score:.1%} complete. Additional testing may be required.**"
        
        return summary
    
    def _generate_methodology_section(self, report_data: Dict[str, Any]) -> str:
        """Genera sezione metodologia"""
        return """
## Methodology

This assessment was conducted using the Heka Collaborative Agent System, an automated penetration testing framework consisting of 7 specialized agents:

1. **A1 - Prompt Generator**: Manages LLM interactions and prompt generation
2. **A2 - Web Researcher**: Conducts web-based reconnaissance and research
3. **A3 - Command Generator**: Generates appropriate Kali Linux commands
4. **A4 - Code Generator**: Creates custom scripts and automation tools
5. **A5 - Terminal Executor**: Executes commands and scripts on Kali Linux
6. **A6 - Task Monitor**: Monitors objective completion and manages cycles
7. **A7 - Report Generator**: Generates comprehensive reports

### Testing Phases:
1. **Reconnaissance**: Information gathering and target analysis
2. **Enumeration**: Service and application enumeration
3. **Vulnerability Assessment**: Identification of security weaknesses
4. **Exploitation**: Attempting to exploit identified vulnerabilities
5. **Reporting**: Documentation of findings and recommendations

### Tools Used:
- Nmap for port scanning and service detection
- Gobuster/Dirb for directory enumeration
- Nikto for web vulnerability scanning
- Custom scripts for specific testing scenarios
"""
    
    def _generate_scope_section(self, report_data: Dict[str, Any]) -> str:
        """Genera sezione scope"""
        target = report_data["metadata"]["target"]
        objective = report_data["metadata"]["objective"]
        
        return f"""
## Scope and Limitations

### Scope:
- **Target**: {target}
- **Objective**: {objective}
- **Testing Type**: Automated penetration testing
- **Testing Approach**: Black-box testing

### Limitations:
- This assessment was conducted using automated tools and may not identify all vulnerabilities
- Manual verification of findings is recommended
- Social engineering and physical security were not tested
- Testing was limited to the specified target and timeframe
- Some tests may have been limited by network restrictions or security controls

### Disclaimer:
This assessment was conducted for security testing purposes only. All activities were performed in accordance with authorized testing parameters.
"""
    
    def _generate_findings_section(self, report_data: Dict[str, Any]) -> str:
        """Genera sezione findings"""
        findings = report_data.get("findings", [])
        
        if not findings:
            return """
## Findings

No significant technical findings were identified during the assessment.
"""
        
        content = "## Findings\n\n"
        
        for i, finding in enumerate(findings, 1):
            content += f"""
### Finding {i}: {finding.get('title', 'Unknown Finding')}

**Category**: {finding.get('category', 'Unknown')}
**Tool**: {finding.get('tool', 'Unknown')}

**Description**: {finding.get('description', 'No description available')}

**Evidence**:
```
{finding.get('evidence', 'No evidence available')}
```

---
"""
        
        return content
    
    def _generate_vulnerabilities_section(self, report_data: Dict[str, Any]) -> str:
        """Genera sezione vulnerabilità"""
        vulnerabilities = report_data.get("vulnerabilities", [])
        
        if not vulnerabilities:
            return """
## Vulnerabilities

No vulnerabilities were identified during the assessment.
"""
        
        content = "## Vulnerabilities\n\n"
        
        # Ordina per severità
        severity_order = ["critical", "high", "medium", "low", "info"]
        sorted_vulns = sorted(vulnerabilities, 
                            key=lambda v: severity_order.index(v.get("severity", "info")))
        
        for i, vuln in enumerate(sorted_vulns, 1):
            severity = vuln.get("severity", "info")
            severity_label = self.severity_levels[severity]["label"]
            
            content += f"""
### Vulnerability {i}: {vuln.get('title', 'Unknown Vulnerability')}

**Severity**: {severity_label.upper()}
**Location**: {vuln.get('location', 'Unknown')}
**Source**: {vuln.get('source', 'Unknown')}

**Description**: {vuln.get('description', 'No description available')}

**Evidence**:
```
{vuln.get('evidence', 'No evidence available')}
```

---
"""
        
        return content
    
    def _generate_recommendations_section(self, report_data: Dict[str, Any]) -> str:
        """Genera sezione raccomandazioni"""
        recommendations = report_data.get("recommendations", [])
        
        content = "## Recommendations\n\n"
        
        if not recommendations:
            content += "No specific recommendations were generated.\n"
            return content
        
        for i, rec in enumerate(recommendations, 1):
            content += f"{i}. {rec}\n"
        
        content += """
### General Security Recommendations:
1. Implement regular security assessments and penetration testing
2. Keep all systems and applications updated with latest security patches
3. Implement strong authentication and access controls
4. Monitor and log security events
5. Provide security awareness training for staff
6. Implement network segmentation and defense in depth
"""
        
        return content
    
    def _generate_technical_section(self, report_data: Dict[str, Any]) -> str:
        """Genera sezione dettagli tecnici"""
        technical = report_data.get("technical_details", {})
        
        content = "## Technical Details\n\n"
        
        # Web Research Details
        web_research = technical.get("web_research", {})
        if web_research:
            content += "### Web Research Results\n"
            content += f"- Technologies identified: {len(web_research.get('technologies', []))}\n"
            content += f"- Subdomains found: {len(web_research.get('subdomains', []))}\n"
            content += f"- Vulnerabilities detected: {len(web_research.get('vulnerabilities', []))}\n\n"
        
        # Command Execution Details
        commands = technical.get("commands_generated", {})
        if commands:
            content += "### Command Generation\n"
            content += f"- Total commands generated: {commands.get('total_commands', 0)}\n"
            content += f"- Command categories: {', '.join(commands.get('categories', []))}\n\n"
        
        # Script Generation Details
        scripts = technical.get("scripts_generated", {})
        if scripts:
            content += "### Script Generation\n"
            content += f"- Total scripts generated: {scripts.get('total_scripts', 0)}\n"
            content += f"- Script types: {', '.join(scripts.get('script_types', []))}\n"
            content += f"- Languages used: {', '.join(scripts.get('languages', []))}\n\n"
        
        # Execution Results
        execution = technical.get("execution_results", {})
        if execution:
            content += "### Execution Statistics\n"
            content += f"- Commands executed: {execution.get('commands_executed', 0)}\n"
            content += f"- Successful executions: {execution.get('successful_executions', 0)}\n"
            content += f"- Failed executions: {execution.get('failed_executions', 0)}\n\n"
        
        return content
    
    async def _generate_markdown_report(self, report_data: Dict[str, Any], 
                                      sections: List[ReportSection]) -> str:
        """Genera report completo in Markdown"""
        target = report_data["metadata"]["target"]
        date = report_data["metadata"]["scan_date"][:10]
        
        markdown = f"""# Penetration Testing Report

**Target**: {target}  
**Date**: {date}  
**Generated by**: Heka Collaborative Agent System  

---

"""
        
        # Aggiungi tutte le sezioni
        for section in sections:
            markdown += section.content + "\n\n"
        
        # Aggiungi appendice con raw data
        markdown += """
## Appendix

### Raw Data
The complete raw data from all agents is available in the accompanying JSON report file.

---

*This report was automatically generated by the Heka Collaborative Agent System.*
"""
        
        return markdown
    
    async def _save_markdown_report(self, markdown_content: str, target: str) -> str:
        """Salva report Markdown"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"pentest_report_{target}_{timestamp}.md"
        filepath = os.path.join(self.reports_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        self.logger.info(f"Report Markdown salvato: {filepath}")
        return filepath
    
    async def _generate_pdf_report(self, markdown_path: str, target: str) -> Optional[str]:
        """Genera PDF dal Markdown usando pandoc"""
        try:
            import subprocess
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            pdf_filename = f"pentest_report_{target}_{timestamp}.pdf"
            pdf_path = os.path.join(self.reports_dir, pdf_filename)
            
            # Comando pandoc
            cmd = [
                "pandoc",
                markdown_path,
                "-o", pdf_path,
                "--pdf-engine=xelatex",
                "-V", "geometry:margin=1in",
                "-V", "fontsize=11pt"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.logger.info(f"Report PDF generato: {pdf_path}")
                return pdf_path
            else:
                self.logger.warning(f"Errore generazione PDF: {result.stderr}")
                return None
                
        except Exception as e:
            self.logger.warning(f"Impossibile generare PDF: {e}")
            return None
    
    async def _save_json_report(self, report_data: Dict[str, Any], target: str) -> str:
        """Salva report in formato JSON"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"pentest_data_{target}_{timestamp}.json"
        filepath = os.path.join(self.reports_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Report JSON salvato: {filepath}")
        return filepath
    
    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche specifiche di A7"""
        base_stats = super().get_stats()
        
        a7_stats = {
            "reports_generated": self.tasks_completed,
            "report_sections": len(self.report_template),
            "severity_levels": len(self.severity_levels),
            "reports_directory": self.reports_dir
        }
        
        base_stats.update(a7_stats)
        return base_stats
