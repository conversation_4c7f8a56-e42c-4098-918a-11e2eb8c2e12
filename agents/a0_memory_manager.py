"""
Agent A0 - Memory Manager per il sistema di agenti collaborativi Heka
Gestisce la memorizzazione e condivisione di tutte le informazioni tra gli agenti
"""

import asyncio
import logging
import json
import os
import sqlite3
import pickle
from typing import Dict, Any, Optional, List, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
import hashlib
import threading

from .core.base_agent import BaseAgent, AgentMessage, MessageType, AgentStatus, TaskResult

class InformationType(Enum):
    """Tipi di informazioni gestite dal memory manager"""
    TARGET_INFO = "target_info"
    VULNERABILITY = "vulnerability"
    COMMAND_RESULT = "command_result"
    WEB_RESEARCH = "web_research"
    GENERATED_CODE = "generated_code"
    EXECUTION_LOG = "execution_log"
    ANALYSIS_RESULT = "analysis_result"
    REPORT_DATA = "report_data"
    AGENT_STATE = "agent_state"
    CONTEXT_DATA = "context_data"

class InformationPriority(Enum):
    """Priorità delle informazioni"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class MemoryEntry:
    """Singola entry nella memoria del sistema"""
    id: str = field(default_factory=lambda: str(hashlib.md5(str(datetime.now()).encode()).hexdigest()))
    source_agent: str = ""
    target_agent: str = ""  # Vuoto se per tutti
    info_type: InformationType = InformationType.CONTEXT_DATA
    priority: InformationPriority = InformationPriority.MEDIUM
    content: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    tags: Set[str] = field(default_factory=set)
    related_entries: List[str] = field(default_factory=list)
    access_count: int = 0
    last_accessed: Optional[datetime] = None

@dataclass
class KnowledgeGraph:
    """Grafo delle conoscenze per relazioni tra informazioni"""
    nodes: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    edges: Dict[str, List[str]] = field(default_factory=dict)
    
class A0MemoryManager(BaseAgent):
    """Agent A0 - Memory Manager e Knowledge Base"""
    
    def __init__(self):
        super().__init__(
            agent_id="a0_memory_manager",
            name="Memory Manager & Knowledge Base",
            description="Gestisce memoria centralizzata e condivisione informazioni tra agenti"
        )
        
        # Database SQLite per persistenza
        self.db_path = "data/heka_memory.db"
        self.db_lock = threading.Lock()
        
        # Cache in memoria per accesso rapido
        self.memory_cache: Dict[str, MemoryEntry] = {}
        self.cache_max_size = 1000
        self.cache_ttl = timedelta(hours=1)
        
        # Knowledge Graph per relazioni
        self.knowledge_graph = KnowledgeGraph()
        
        # Indici per ricerca rapida
        self.type_index: Dict[InformationType, Set[str]] = {}
        self.agent_index: Dict[str, Set[str]] = {}
        self.tag_index: Dict[str, Set[str]] = {}
        self.target_index: Dict[str, Set[str]] = {}
        
        # Statistiche
        self.total_entries = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Configurazione
        self.auto_cleanup_interval = timedelta(hours=6)
        self.max_entries_per_agent = 500
        self.enable_compression = True
        
        # Directory per backup
        self.backup_dir = "data/backups"
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs("data", exist_ok=True)
        
    async def initialize(self):
        """Inizializza il memory manager"""
        try:
            self.logger.info("Inizializzazione Memory Manager A0...")
            
            # Inizializza database
            await self._initialize_database()
            
            # Carica cache da database
            await self._load_cache_from_db()
            
            # Avvia task di cleanup automatico
            asyncio.create_task(self._auto_cleanup_task())
            
            # Registra handlers specifici
            self._register_memory_handlers()
            
            self.logger.info(f"Memory Manager inizializzato - {self.total_entries} entries caricate")
            
        except Exception as e:
            self.logger.error(f"Errore inizializzazione A0: {e}")
            raise

    async def shutdown(self):
        """Chiude l'agente A0 e salva lo stato"""
        try:
            self.logger.info("Shutdown A0 Memory Manager...")

            # Salva cache su database
            await self._flush_cache_to_db()

            # Backup finale
            backup_path = await self._create_backup_if_needed()
            if backup_path:
                self.logger.info(f"Backup finale creato: {backup_path}")

            # Chiudi connessioni database
            # (SQLite si chiude automaticamente)

            # Pulisci cache
            self.memory_cache.clear()

            self.logger.info("A0 Memory Manager chiuso correttamente")

        except Exception as e:
            self.logger.error(f"Errore shutdown A0: {e}")

    async def _flush_cache_to_db(self):
        """Salva tutte le entries in cache nel database"""
        try:
            for entry in self.memory_cache.values():
                await self._save_entry(entry)

            self.logger.debug(f"Cache salvata: {len(self.memory_cache)} entries")

        except Exception as e:
            self.logger.error(f"Errore flush cache: {e}")
    
    async def execute_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """Esegue task di gestione memoria"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self.update_status(AgentStatus.WORKING)
            
            task_type = task_data.get("task_type", "memory_management")
            
            if task_type == "memory_management":
                result = await self._manage_memory_task(task_data)
            elif task_type == "information_sharing":
                result = await self._share_information_task(task_data)
            elif task_type == "knowledge_extraction":
                result = await self._extract_knowledge_task(task_data)
            else:
                raise ValueError(f"Task type non supportato: {task_type}")
            
            execution_time = asyncio.get_event_loop().time() - start_time
            
            await self.update_status(AgentStatus.COMPLETED)
            
            return TaskResult(
                success=True,
                data=result,
                execution_time=execution_time,
                metadata={"task_type": task_type}
            )
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione task A0: {e}")
            await self.update_status(AgentStatus.ERROR)
            
            return TaskResult(
                success=False,
                error=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )

    # ==================== METODI PUBBLICI API ====================

    async def store_information(self, source_agent: str, info_type: InformationType,
                              content: Dict[str, Any], target_agent: str = "",
                              priority: InformationPriority = InformationPriority.MEDIUM,
                              tags: Set[str] = None, expires_in: timedelta = None,
                              metadata: Dict[str, Any] = None) -> str:
        """Memorizza nuove informazioni nel sistema"""
        try:
            # Crea entry
            entry = MemoryEntry(
                source_agent=source_agent,
                target_agent=target_agent,
                info_type=info_type,
                priority=priority,
                content=content,
                metadata=metadata or {},
                tags=tags or set(),
                expires_at=datetime.now() + expires_in if expires_in else None
            )

            # Salva in cache e database
            await self._save_entry(entry)

            # Aggiorna indici
            self._update_indices(entry)

            # Notifica agenti interessati
            await self._notify_agents_of_new_info(entry)

            self.logger.debug(f"Informazione memorizzata: {entry.id} da {source_agent}")
            return entry.id

        except Exception as e:
            self.logger.error(f"Errore memorizzazione informazione: {e}")
            raise

    async def retrieve_information(self, requester_agent: str,
                                 info_type: InformationType = None,
                                 source_agent: str = None,
                                 tags: Set[str] = None,
                                 since: datetime = None,
                                 limit: int = 100) -> List[MemoryEntry]:
        """Recupera informazioni dal sistema"""
        try:
            # Costruisci query
            query_filters = {
                "info_type": info_type,
                "source_agent": source_agent,
                "tags": tags,
                "since": since,
                "target_agent": requester_agent
            }

            # Cerca in cache prima
            results = await self._search_cache(query_filters, limit)

            if not results:
                # Cerca in database
                results = await self._search_database(query_filters, limit)

                # Aggiorna cache
                for entry in results:
                    self._add_to_cache(entry)

            # Aggiorna statistiche accesso
            for entry in results:
                entry.access_count += 1
                entry.last_accessed = datetime.now()

            self.logger.debug(f"Recuperate {len(results)} informazioni per {requester_agent}")
            return results

        except Exception as e:
            self.logger.error(f"Errore recupero informazioni: {e}")
            return []

    async def get_related_information(self, entry_id: str,
                                    max_depth: int = 2) -> List[MemoryEntry]:
        """Recupera informazioni correlate usando il knowledge graph"""
        try:
            related_ids = self._find_related_entries(entry_id, max_depth)
            related_entries = []

            for rel_id in related_ids:
                entry = await self._get_entry_by_id(rel_id)
                if entry:
                    related_entries.append(entry)

            return related_entries

        except Exception as e:
            self.logger.error(f"Errore recupero informazioni correlate: {e}")
            return []

    async def update_information(self, entry_id: str, updates: Dict[str, Any],
                               updater_agent: str) -> bool:
        """Aggiorna informazioni esistenti"""
        try:
            entry = await self._get_entry_by_id(entry_id)
            if not entry:
                return False

            # Verifica permessi (solo source agent può aggiornare)
            if entry.source_agent != updater_agent:
                self.logger.warning(f"Agente {updater_agent} non autorizzato ad aggiornare {entry_id}")
                return False

            # Applica aggiornamenti
            for key, value in updates.items():
                if hasattr(entry, key):
                    setattr(entry, key, value)

            # Salva modifiche
            await self._save_entry(entry)

            self.logger.debug(f"Informazione {entry_id} aggiornata da {updater_agent}")
            return True

        except Exception as e:
            self.logger.error(f"Errore aggiornamento informazione: {e}")
            return False

    async def delete_information(self, entry_id: str, deleter_agent: str) -> bool:
        """Elimina informazioni dal sistema"""
        try:
            entry = await self._get_entry_by_id(entry_id)
            if not entry:
                return False

            # Verifica permessi
            if entry.source_agent != deleter_agent:
                self.logger.warning(f"Agente {deleter_agent} non autorizzato ad eliminare {entry_id}")
                return False

            # Rimuovi da cache e database
            await self._delete_entry(entry_id)

            # Aggiorna indici
            self._remove_from_indices(entry)

            self.logger.debug(f"Informazione {entry_id} eliminata da {deleter_agent}")
            return True

        except Exception as e:
            self.logger.error(f"Errore eliminazione informazione: {e}")
            return False

    # ==================== METODI DATABASE ====================

    async def _initialize_database(self):
        """Inizializza il database SQLite"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # Tabella principale per le entries
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS memory_entries (
                        id TEXT PRIMARY KEY,
                        source_agent TEXT NOT NULL,
                        target_agent TEXT,
                        info_type TEXT NOT NULL,
                        priority INTEGER NOT NULL,
                        content BLOB NOT NULL,
                        metadata BLOB,
                        timestamp TEXT NOT NULL,
                        expires_at TEXT,
                        tags TEXT,
                        related_entries TEXT,
                        access_count INTEGER DEFAULT 0,
                        last_accessed TEXT
                    )
                ''')

                # Tabella per il knowledge graph
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS knowledge_graph (
                        node_id TEXT PRIMARY KEY,
                        node_data BLOB,
                        edges TEXT
                    )
                ''')

                # Indici per performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_source_agent ON memory_entries(source_agent)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_info_type ON memory_entries(info_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON memory_entries(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_target_agent ON memory_entries(target_agent)')

                conn.commit()
                conn.close()

                self.logger.info("Database inizializzato correttamente")

        except Exception as e:
            self.logger.error(f"Errore inizializzazione database: {e}")
            raise

    async def _save_entry(self, entry: MemoryEntry):
        """Salva entry nel database e cache"""
        try:
            # Serializza dati complessi
            content_blob = pickle.dumps(entry.content) if self.enable_compression else json.dumps(entry.content).encode()
            metadata_blob = pickle.dumps(entry.metadata) if self.enable_compression else json.dumps(entry.metadata).encode()

            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT OR REPLACE INTO memory_entries
                    (id, source_agent, target_agent, info_type, priority, content, metadata,
                     timestamp, expires_at, tags, related_entries, access_count, last_accessed)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    entry.id,
                    entry.source_agent,
                    entry.target_agent,
                    entry.info_type.value,
                    entry.priority.value,
                    content_blob,
                    metadata_blob,
                    entry.timestamp.isoformat(),
                    entry.expires_at.isoformat() if entry.expires_at else None,
                    json.dumps(list(entry.tags)),
                    json.dumps(entry.related_entries),
                    entry.access_count,
                    entry.last_accessed.isoformat() if entry.last_accessed else None
                ))

                conn.commit()
                conn.close()

            # Aggiungi alla cache
            self._add_to_cache(entry)
            self.total_entries += 1

        except Exception as e:
            self.logger.error(f"Errore salvataggio entry: {e}")
            raise

    async def _get_entry_by_id(self, entry_id: str) -> Optional[MemoryEntry]:
        """Recupera entry per ID"""
        try:
            # Cerca prima in cache
            if entry_id in self.memory_cache:
                self.cache_hits += 1
                return self.memory_cache[entry_id]

            # Cerca in database
            self.cache_misses += 1
            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute('SELECT * FROM memory_entries WHERE id = ?', (entry_id,))
                row = cursor.fetchone()
                conn.close()

                if row:
                    entry = self._row_to_entry(row)
                    self._add_to_cache(entry)
                    return entry

            return None

        except Exception as e:
            self.logger.error(f"Errore recupero entry {entry_id}: {e}")
            return None

    async def _delete_entry(self, entry_id: str):
        """Elimina entry dal database e cache"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('DELETE FROM memory_entries WHERE id = ?', (entry_id,))
                conn.commit()
                conn.close()

            # Rimuovi dalla cache
            if entry_id in self.memory_cache:
                del self.memory_cache[entry_id]

            self.total_entries -= 1

        except Exception as e:
            self.logger.error(f"Errore eliminazione entry {entry_id}: {e}")
            raise

    # ==================== METODI CACHE E INDICI ====================

    def _add_to_cache(self, entry: MemoryEntry):
        """Aggiunge entry alla cache"""
        try:
            # Controlla dimensione cache
            if len(self.memory_cache) >= self.cache_max_size:
                self._evict_cache_entries()

            self.memory_cache[entry.id] = entry

        except Exception as e:
            self.logger.error(f"Errore aggiunta cache: {e}")

    def _evict_cache_entries(self):
        """Rimuove entries meno utilizzate dalla cache"""
        try:
            # Ordina per ultimo accesso e rimuovi il 20%
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: (x[1].last_accessed or datetime.min, x[1].access_count)
            )

            entries_to_remove = len(sorted_entries) // 5
            for i in range(entries_to_remove):
                entry_id = sorted_entries[i][0]
                del self.memory_cache[entry_id]

            self.logger.debug(f"Rimossi {entries_to_remove} entries dalla cache")

        except Exception as e:
            self.logger.error(f"Errore eviction cache: {e}")

    def _update_indices(self, entry: MemoryEntry):
        """Aggiorna indici per ricerca rapida"""
        try:
            # Indice per tipo
            if entry.info_type not in self.type_index:
                self.type_index[entry.info_type] = set()
            self.type_index[entry.info_type].add(entry.id)

            # Indice per agente
            if entry.source_agent not in self.agent_index:
                self.agent_index[entry.source_agent] = set()
            self.agent_index[entry.source_agent].add(entry.id)

            # Indice per tag
            for tag in entry.tags:
                if tag not in self.tag_index:
                    self.tag_index[tag] = set()
                self.tag_index[tag].add(entry.id)

            # Indice per target (se presente nel content)
            target = entry.content.get("target")
            if target:
                if target not in self.target_index:
                    self.target_index[target] = set()
                self.target_index[target].add(entry.id)

        except Exception as e:
            self.logger.error(f"Errore aggiornamento indici: {e}")

    def _remove_from_indices(self, entry: MemoryEntry):
        """Rimuove entry dagli indici"""
        try:
            # Rimuovi da indice tipo
            if entry.info_type in self.type_index:
                self.type_index[entry.info_type].discard(entry.id)

            # Rimuovi da indice agente
            if entry.source_agent in self.agent_index:
                self.agent_index[entry.source_agent].discard(entry.id)

            # Rimuovi da indice tag
            for tag in entry.tags:
                if tag in self.tag_index:
                    self.tag_index[tag].discard(entry.id)

            # Rimuovi da indice target
            target = entry.content.get("target")
            if target and target in self.target_index:
                self.target_index[target].discard(entry.id)

        except Exception as e:
            self.logger.error(f"Errore rimozione indici: {e}")

    async def _search_cache(self, filters: Dict[str, Any], limit: int) -> List[MemoryEntry]:
        """Cerca entries nella cache"""
        try:
            results = []

            for entry in self.memory_cache.values():
                if self._matches_filters(entry, filters):
                    results.append(entry)
                    if len(results) >= limit:
                        break

            return results

        except Exception as e:
            self.logger.error(f"Errore ricerca cache: {e}")
            return []

    async def _search_database(self, filters: Dict[str, Any], limit: int) -> List[MemoryEntry]:
        """Cerca entries nel database"""
        try:
            query = "SELECT * FROM memory_entries WHERE 1=1"
            params = []

            # Costruisci query dinamica
            if filters.get("info_type"):
                query += " AND info_type = ?"
                params.append(filters["info_type"].value)

            if filters.get("source_agent"):
                query += " AND source_agent = ?"
                params.append(filters["source_agent"])

            if filters.get("target_agent"):
                query += " AND (target_agent = ? OR target_agent = '')"
                params.append(filters["target_agent"])

            if filters.get("since"):
                query += " AND timestamp >= ?"
                params.append(filters["since"].isoformat())

            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)

            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()
                conn.close()

            results = []
            for row in rows:
                entry = self._row_to_entry(row)
                if self._matches_filters(entry, filters):
                    results.append(entry)

            return results

        except Exception as e:
            self.logger.error(f"Errore ricerca database: {e}")
            return []

    # ==================== METODI UTILITÀ ====================

    def _row_to_entry(self, row) -> MemoryEntry:
        """Converte riga database in MemoryEntry"""
        try:
            # Deserializza dati
            content = pickle.loads(row[5]) if self.enable_compression else json.loads(row[5].decode())
            metadata = pickle.loads(row[6]) if row[6] and self.enable_compression else json.loads(row[6].decode()) if row[6] else {}

            return MemoryEntry(
                id=row[0],
                source_agent=row[1],
                target_agent=row[2] or "",
                info_type=InformationType(row[3]),
                priority=InformationPriority(row[4]),
                content=content,
                metadata=metadata,
                timestamp=datetime.fromisoformat(row[7]),
                expires_at=datetime.fromisoformat(row[8]) if row[8] else None,
                tags=set(json.loads(row[9])) if row[9] else set(),
                related_entries=json.loads(row[10]) if row[10] else [],
                access_count=row[11] or 0,
                last_accessed=datetime.fromisoformat(row[12]) if row[12] else None
            )

        except Exception as e:
            self.logger.error(f"Errore conversione riga: {e}")
            raise

    def _matches_filters(self, entry: MemoryEntry, filters: Dict[str, Any]) -> bool:
        """Verifica se entry corrisponde ai filtri"""
        try:
            # Filtro tipo
            if filters.get("info_type") and entry.info_type != filters["info_type"]:
                return False

            # Filtro agente sorgente
            if filters.get("source_agent") and entry.source_agent != filters["source_agent"]:
                return False

            # Filtro agente target
            if filters.get("target_agent"):
                target = filters["target_agent"]
                if entry.target_agent and entry.target_agent != target:
                    return False

            # Filtro tag
            if filters.get("tags"):
                if not entry.tags.intersection(filters["tags"]):
                    return False

            # Filtro data
            if filters.get("since") and entry.timestamp < filters["since"]:
                return False

            # Verifica scadenza
            if entry.expires_at and entry.expires_at < datetime.now():
                return False

            return True

        except Exception as e:
            self.logger.error(f"Errore verifica filtri: {e}")
            return False

    def _register_memory_handlers(self):
        """Registra handlers specifici per messaggi di memoria"""
        try:
            # Handler per richieste di memorizzazione
            async def handle_store_request(message: AgentMessage):
                content = message.content
                await self.store_information(
                    source_agent=message.sender_id,
                    info_type=InformationType(content.get("info_type", "context_data")),
                    content=content.get("data", {}),
                    target_agent=content.get("target_agent", ""),
                    priority=InformationPriority(content.get("priority", 2)),
                    tags=set(content.get("tags", [])),
                    metadata=content.get("metadata", {})
                )

            # Handler per richieste di recupero
            async def handle_retrieve_request(message: AgentMessage):
                content = message.content
                results = await self.retrieve_information(
                    requester_agent=message.sender_id,
                    info_type=InformationType(content["info_type"]) if content.get("info_type") else None,
                    source_agent=content.get("source_agent"),
                    tags=set(content.get("tags", [])) if content.get("tags") else None,
                    since=datetime.fromisoformat(content["since"]) if content.get("since") else None,
                    limit=content.get("limit", 100)
                )

                # Invia risposta
                await self.send_message(
                    receiver_id=message.sender_id,
                    message_type=MessageType.TASK_RESPONSE,
                    content={"results": [asdict(entry) for entry in results]}
                )

            # Registra handlers
            self.message_handlers[MessageType.DATA_SHARE] = handle_store_request
            self.message_handlers[MessageType.TASK_REQUEST] = handle_retrieve_request

        except Exception as e:
            self.logger.error(f"Errore registrazione handlers: {e}")

    async def _notify_agents_of_new_info(self, entry: MemoryEntry):
        """Notifica agenti di nuove informazioni rilevanti"""
        try:
            # Se target specifico, notifica solo quello
            if entry.target_agent:
                await self.send_message(
                    receiver_id=entry.target_agent,
                    message_type=MessageType.DATA_SHARE,
                    content={
                        "new_information": True,
                        "entry_id": entry.id,
                        "info_type": entry.info_type.value,
                        "source_agent": entry.source_agent,
                        "priority": entry.priority.value,
                        "summary": entry.metadata.get("summary", "")
                    }
                )
            else:
                # Broadcast a tutti gli agenti interessati
                if self.coordinator:
                    await self.coordinator.broadcast_message(
                        message_type=MessageType.DATA_SHARE,
                        content={
                            "new_information": True,
                            "entry_id": entry.id,
                            "info_type": entry.info_type.value,
                            "source_agent": entry.source_agent,
                            "priority": entry.priority.value,
                            "summary": entry.metadata.get("summary", "")
                        },
                        exclude_agents=[entry.source_agent, self.agent_id]
                    )

        except Exception as e:
            self.logger.error(f"Errore notifica agenti: {e}")

    # ==================== TASK SPECIFICI ====================

    async def _manage_memory_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Gestisce task di memory management"""
        try:
            # Cleanup automatico
            cleaned_entries = await self._cleanup_expired_entries()

            # Ottimizzazione indici
            await self._optimize_indices()

            # Backup se necessario
            backup_path = await self._create_backup_if_needed()

            # Statistiche
            stats = await self._get_memory_statistics()

            return {
                "cleaned_entries": cleaned_entries,
                "backup_created": backup_path is not None,
                "backup_path": backup_path,
                "statistics": stats
            }

        except Exception as e:
            self.logger.error(f"Errore task memory management: {e}")
            raise

    async def _share_information_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Gestisce task di condivisione informazioni"""
        try:
            objective = task_data.get("objective", "")
            target = task_data.get("target", "")
            shared_context = task_data.get("shared_context", {})
            previous_results = task_data.get("previous_results", {})

            # Memorizza risultati di tutti gli agenti precedenti
            stored_entries = []
            for agent_id, results in previous_results.items():
                if results and agent_id != self.agent_id:
                    entry_id = await self.store_information(
                        source_agent=agent_id,
                        info_type=self._determine_info_type(agent_id, results),
                        content=results,
                        priority=InformationPriority.HIGH,
                        tags={target, objective, "pentest_session"},
                        metadata={
                            "session_id": shared_context.get("session_id"),
                            "target": target,
                            "objective": objective,
                            "timestamp": datetime.now().isoformat()
                        }
                    )
                    stored_entries.append(entry_id)

            # Recupera informazioni correlate per il contesto
            related_info = await self._get_contextual_information(target, objective)

            return {
                "stored_entries": stored_entries,
                "related_information": [asdict(entry) for entry in related_info],
                "context_enhanced": True
            }

        except Exception as e:
            self.logger.error(f"Errore task condivisione: {e}")
            raise

    async def _extract_knowledge_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Estrae conoscenza e pattern dai dati memorizzati"""
        try:
            target = task_data.get("target", "")

            # Recupera tutte le informazioni sul target
            target_entries = await self.retrieve_information(
                requester_agent=self.agent_id,
                tags={target}
            )

            # Analizza pattern e correlazioni
            patterns = self._analyze_patterns(target_entries)

            # Aggiorna knowledge graph
            self._update_knowledge_graph(target_entries, patterns)

            return {
                "patterns_found": patterns,
                "total_entries_analyzed": len(target_entries),
                "knowledge_graph_updated": True
            }

        except Exception as e:
            self.logger.error(f"Errore task estrazione conoscenza: {e}")
            raise

    # ==================== METODI AUSILIARI ====================

    def _determine_info_type(self, agent_id: str, results: Dict[str, Any]) -> InformationType:
        """Determina il tipo di informazione basato sull'agente"""
        type_mapping = {
            "a1_prompt_generator": InformationType.ANALYSIS_RESULT,
            "a2_web_researcher": InformationType.WEB_RESEARCH,
            "a3_command_generator": InformationType.COMMAND_RESULT,
            "a4_code_generator": InformationType.GENERATED_CODE,
            "a5_terminal_executor": InformationType.EXECUTION_LOG,
            "a6_task_monitor": InformationType.ANALYSIS_RESULT,
            "a7_report_generator": InformationType.REPORT_DATA
        }
        return type_mapping.get(agent_id, InformationType.CONTEXT_DATA)

    async def _get_contextual_information(self, target: str, objective: str) -> List[MemoryEntry]:
        """Recupera informazioni contestuali rilevanti"""
        try:
            # Cerca informazioni correlate al target
            target_info = await self.retrieve_information(
                requester_agent=self.agent_id,
                tags={target},
                limit=50
            )

            # Cerca informazioni su obiettivi simili
            objective_info = await self.retrieve_information(
                requester_agent=self.agent_id,
                tags={objective.split()[0]},  # Prima parola dell'obiettivo
                limit=20
            )

            # Combina e deduplica
            all_info = {entry.id: entry for entry in target_info + objective_info}
            return list(all_info.values())

        except Exception as e:
            self.logger.error(f"Errore recupero informazioni contestuali: {e}")
            return []

    def _analyze_patterns(self, entries: List[MemoryEntry]) -> Dict[str, Any]:
        """Analizza pattern nelle informazioni"""
        try:
            patterns = {
                "common_vulnerabilities": [],
                "frequent_ports": [],
                "common_technologies": [],
                "attack_vectors": []
            }

            # Analizza vulnerabilità comuni
            vuln_entries = [e for e in entries if e.info_type == InformationType.VULNERABILITY]
            if vuln_entries:
                vuln_types = [e.content.get("type") for e in vuln_entries if e.content.get("type")]
                patterns["common_vulnerabilities"] = list(set(vuln_types))

            # Analizza porte frequenti
            for entry in entries:
                if "ports" in entry.content:
                    patterns["frequent_ports"].extend(entry.content["ports"])

            patterns["frequent_ports"] = list(set(patterns["frequent_ports"]))

            return patterns

        except Exception as e:
            self.logger.error(f"Errore analisi pattern: {e}")
            return {}

    async def _cleanup_expired_entries(self) -> int:
        """Rimuove entries scadute"""
        try:
            now = datetime.now()
            cleaned = 0

            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('DELETE FROM memory_entries WHERE expires_at < ?', (now.isoformat(),))
                cleaned = cursor.rowcount
                conn.commit()
                conn.close()

            # Rimuovi dalla cache
            expired_ids = [
                entry_id for entry_id, entry in self.memory_cache.items()
                if entry.expires_at and entry.expires_at < now
            ]

            for entry_id in expired_ids:
                del self.memory_cache[entry_id]

            self.total_entries -= cleaned
            self.logger.info(f"Rimosse {cleaned} entries scadute")

            return cleaned

        except Exception as e:
            self.logger.error(f"Errore cleanup: {e}")
            return 0

    async def _optimize_indices(self):
        """Ottimizza gli indici per performance"""
        try:
            # Ricostruisci indici da zero
            self.type_index.clear()
            self.agent_index.clear()
            self.tag_index.clear()
            self.target_index.clear()

            # Ricostruisci da cache
            for entry in self.memory_cache.values():
                self._update_indices(entry)

            self.logger.debug("Indici ottimizzati")

        except Exception as e:
            self.logger.error(f"Errore ottimizzazione indici: {e}")

    async def _create_backup_if_needed(self) -> Optional[str]:
        """Crea backup del database se necessario"""
        try:
            # Controlla se è necessario un backup (ogni 24 ore)
            backup_needed = True
            latest_backup = None

            # Trova ultimo backup
            if os.path.exists(self.backup_dir):
                backups = [f for f in os.listdir(self.backup_dir) if f.endswith('.db')]
                if backups:
                    latest_backup = max(backups)
                    backup_time = datetime.fromtimestamp(
                        os.path.getmtime(os.path.join(self.backup_dir, latest_backup))
                    )
                    if datetime.now() - backup_time < timedelta(hours=24):
                        backup_needed = False

            if backup_needed:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = os.path.join(self.backup_dir, f"heka_memory_backup_{timestamp}.db")

                # Copia database
                import shutil
                shutil.copy2(self.db_path, backup_path)

                self.logger.info(f"Backup creato: {backup_path}")
                return backup_path

            return None

        except Exception as e:
            self.logger.error(f"Errore creazione backup: {e}")
            return None

    async def _get_memory_statistics(self) -> Dict[str, Any]:
        """Ottieni statistiche della memoria"""
        try:
            stats = {
                "total_entries": self.total_entries,
                "cache_size": len(self.memory_cache),
                "cache_hit_ratio": self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
                "entries_by_type": {},
                "entries_by_agent": {},
                "database_size_mb": os.path.getsize(self.db_path) / (1024 * 1024) if os.path.exists(self.db_path) else 0
            }

            # Statistiche per tipo
            for info_type, entry_ids in self.type_index.items():
                stats["entries_by_type"][info_type.value] = len(entry_ids)

            # Statistiche per agente
            for agent_id, entry_ids in self.agent_index.items():
                stats["entries_by_agent"][agent_id] = len(entry_ids)

            return stats

        except Exception as e:
            self.logger.error(f"Errore statistiche: {e}")
            return {}

    async def _load_cache_from_db(self):
        """Carica entries più recenti in cache"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # Carica entries più recenti e più accedute
                cursor.execute('''
                    SELECT * FROM memory_entries
                    ORDER BY access_count DESC, timestamp DESC
                    LIMIT ?
                ''', (self.cache_max_size // 2,))

                rows = cursor.fetchall()
                conn.close()

            for row in rows:
                entry = self._row_to_entry(row)
                self._add_to_cache(entry)
                self._update_indices(entry)

            # Conta totale entries
            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM memory_entries')
                self.total_entries = cursor.fetchone()[0]
                conn.close()

            self.logger.info(f"Cache caricata: {len(self.memory_cache)} entries")

        except Exception as e:
            self.logger.error(f"Errore caricamento cache: {e}")

    def _find_related_entries(self, entry_id: str, max_depth: int) -> Set[str]:
        """Trova entries correlate usando il knowledge graph"""
        try:
            related = set()
            to_explore = {entry_id}
            explored = set()

            for depth in range(max_depth):
                if not to_explore:
                    break

                current_level = to_explore.copy()
                to_explore.clear()

                for current_id in current_level:
                    if current_id in explored:
                        continue

                    explored.add(current_id)

                    # Trova entries correlate negli indici
                    if current_id in self.memory_cache:
                        entry = self.memory_cache[current_id]

                        # Correlazioni per tag
                        for tag in entry.tags:
                            if tag in self.tag_index:
                                related.update(self.tag_index[tag])

                        # Correlazioni esplicite
                        related.update(entry.related_entries)

                        # Aggiungi alla prossima esplorazione
                        to_explore.update(entry.related_entries)

            related.discard(entry_id)  # Rimuovi entry originale
            return related

        except Exception as e:
            self.logger.error(f"Errore ricerca correlazioni: {e}")
            return set()

    def _update_knowledge_graph(self, entries: List[MemoryEntry], patterns: Dict[str, Any]):
        """Aggiorna il knowledge graph con nuove correlazioni"""
        try:
            # Aggiorna nodi
            for entry in entries:
                self.knowledge_graph.nodes[entry.id] = {
                    "type": entry.info_type.value,
                    "agent": entry.source_agent,
                    "tags": list(entry.tags),
                    "timestamp": entry.timestamp.isoformat()
                }

            # Aggiorna edges basati su pattern
            for entry in entries:
                if entry.id not in self.knowledge_graph.edges:
                    self.knowledge_graph.edges[entry.id] = []

                # Collega entries con tag simili
                for other_entry in entries:
                    if entry.id != other_entry.id:
                        common_tags = entry.tags.intersection(other_entry.tags)
                        if len(common_tags) >= 2:  # Almeno 2 tag in comune
                            if other_entry.id not in self.knowledge_graph.edges[entry.id]:
                                self.knowledge_graph.edges[entry.id].append(other_entry.id)

        except Exception as e:
            self.logger.error(f"Errore aggiornamento knowledge graph: {e}")

    async def _auto_cleanup_task(self):
        """Task automatico di cleanup periodico"""
        try:
            while True:
                await asyncio.sleep(self.auto_cleanup_interval.total_seconds())

                if self.status != AgentStatus.ERROR:
                    await self._cleanup_expired_entries()
                    await self._optimize_indices()

                    # Backup periodico
                    await self._create_backup_if_needed()

                    self.logger.debug("Cleanup automatico completato")

        except asyncio.CancelledError:
            self.logger.info("Task cleanup automatico terminato")
        except Exception as e:
            self.logger.error(f"Errore task cleanup automatico: {e}")

    async def get_memory_summary(self) -> Dict[str, Any]:
        """Ottieni riassunto dello stato della memoria"""
        try:
            stats = await self._get_memory_statistics()

            # Aggiungi informazioni aggiuntive
            recent_entries = await self.retrieve_information(
                requester_agent=self.agent_id,
                since=datetime.now() - timedelta(hours=1),
                limit=10
            )

            summary = {
                "statistics": stats,
                "recent_activity": len(recent_entries),
                "cache_efficiency": f"{stats.get('cache_hit_ratio', 0):.2%}",
                "status": self.status.value,
                "uptime": str(datetime.now() - self.start_time)
            }

            return summary

        except Exception as e:
            self.logger.error(f"Errore riassunto memoria: {e}")
            return {"error": str(e)}
