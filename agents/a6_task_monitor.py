"""
Agent A6 - Task Completion Monitor
Responsabile del controllo completamento obiettivi e gestione cicli di esecuzione
"""

import asyncio
import logging
import json
import re
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from .core.base_agent import BaseAgent, AgentStatus, TaskResult, MessageType

class ObjectiveAnalysis:
    """Analisi dello stato di completamento di un obiettivo"""
    def __init__(self, objective: str):
        self.objective = objective
        self.completion_score = 0.0  # 0.0 - 1.0
        self.completion_criteria = []
        self.met_criteria = []
        self.missing_criteria = []
        self.evidence = []
        self.recommendations = []

class A6TaskMonitor(BaseAgent):
    """Agent A6 - Monitor completamento task e obiettivi"""
    
    def __init__(self):
        super().__init__(
            agent_id="a6_task_monitor",
            name="Task Completion Monitor",
            description="Monitora completamento obiettivi e gestisce cicli di esecuzione"
        )
        
        # Criteri di completamento per diversi tipi di obiettivi
        self.completion_criteria = {
            "reconnaissance": {
                "port_scan_completed": {"weight": 0.3, "description": "Scansione porte completata"},
                "service_enumeration": {"weight": 0.2, "description": "Enumerazione servizi completata"},
                "web_enumeration": {"weight": 0.2, "description": "Enumerazione web completata"},
                "subdomain_discovery": {"weight": 0.15, "description": "Scoperta subdomain completata"},
                "technology_identification": {"weight": 0.15, "description": "Identificazione tecnologie completata"}
            },
            "vulnerability_assessment": {
                "vulnerability_scan": {"weight": 0.4, "description": "Scansione vulnerabilità completata"},
                "web_vulnerabilities": {"weight": 0.3, "description": "Test vulnerabilità web completate"},
                "network_vulnerabilities": {"weight": 0.2, "description": "Test vulnerabilità di rete completate"},
                "configuration_issues": {"weight": 0.1, "description": "Controllo configurazioni completato"}
            },
            "penetration_testing": {
                "initial_access": {"weight": 0.3, "description": "Accesso iniziale ottenuto"},
                "privilege_escalation": {"weight": 0.25, "description": "Escalation privilegi completata"},
                "lateral_movement": {"weight": 0.2, "description": "Movimento laterale eseguito"},
                "data_exfiltration": {"weight": 0.15, "description": "Esfiltrazione dati simulata"},
                "persistence": {"weight": 0.1, "description": "Persistenza stabilita"}
            },
            "web_application_testing": {
                "authentication_bypass": {"weight": 0.25, "description": "Test bypass autenticazione"},
                "injection_attacks": {"weight": 0.25, "description": "Test attacchi injection"},
                "xss_testing": {"weight": 0.2, "description": "Test Cross-Site Scripting"},
                "access_control": {"weight": 0.15, "description": "Test controllo accessi"},
                "business_logic": {"weight": 0.15, "description": "Test logica business"}
            }
        }
        
        # Pattern per identificare successi nei risultati
        self.success_patterns = {
            "port_scan": [
                r"(\d+)/tcp\s+open",
                r"(\d+)/udp\s+open",
                r"Nmap scan report for",
                r"PORT\s+STATE\s+SERVICE"
            ],
            "vulnerability_found": [
                r"VULNERABLE",
                r"CRITICAL",
                r"HIGH",
                r"SQL injection",
                r"Cross-site scripting",
                r"Remote code execution"
            ],
            "access_gained": [
                r"shell",
                r"meterpreter",
                r"root@",
                r"administrator@",
                r"login successful",
                r"authentication successful"
            ],
            "data_found": [
                r"password",
                r"hash",
                r"credential",
                r"database",
                r"config",
                r"backup"
            ]
        }
        
        # Storico analisi
        self.analysis_history = []
        self.max_history = 10
        
    async def initialize(self):
        """Inizializza l'agente A6"""
        try:
            self.logger.info("Agent A6 inizializzato")
            
        except Exception as e:
            self.logger.error(f"Errore inizializzazione A6: {e}")
            raise
    
    async def shutdown(self):
        """Chiude l'agente A6"""
        self.logger.info("Agent A6 chiuso")
    
    async def execute_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """Esegue un task di monitoraggio completamento"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self.update_status(AgentStatus.WORKING)
            
            objective = task_data.get("objective", "")
            target = task_data.get("target", "")
            shared_context = task_data.get("shared_context", {})
            previous_results = task_data.get("previous_results", {})
            
            self.logger.info(f"A6 - Monitoraggio completamento per obiettivo: {objective}")
            
            # Analizza tutti i risultati precedenti
            analysis = await self._analyze_objective_completion(
                objective, target, previous_results
            )
            
            # Determina se l'obiettivo è raggiunto
            objective_reached = analysis.completion_score >= 0.8  # 80% soglia
            
            # Genera raccomandazioni per prossimi passi
            recommendations = await self._generate_recommendations(
                analysis, previous_results, objective_reached
            )
            
            # Richiedi analisi LLM se disponibile
            llm_analysis = await self._request_llm_analysis(
                objective, analysis, previous_results
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            self.total_execution_time += execution_time
            self.tasks_completed += 1
            
            result_data = {
                "objective_reached": objective_reached,
                "completion_score": analysis.completion_score,
                "completion_criteria": analysis.completion_criteria,
                "met_criteria": analysis.met_criteria,
                "missing_criteria": analysis.missing_criteria,
                "evidence": analysis.evidence,
                "recommendations": recommendations,
                "llm_analysis": llm_analysis,
                "should_continue_cycle": not objective_reached,
                "execution_time": execution_time
            }
            
            # Aggiungi all'history
            self.analysis_history.append({
                "timestamp": datetime.now(),
                "objective": objective,
                "analysis": analysis,
                "objective_reached": objective_reached
            })
            
            # Mantieni solo le ultime N analisi
            if len(self.analysis_history) > self.max_history:
                self.analysis_history = self.analysis_history[-self.max_history:]
            
            await self.update_status(AgentStatus.IDLE)
            
            return TaskResult(
                success=True,
                data=result_data,
                execution_time=execution_time
            )
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione task A6: {e}")
            self.tasks_failed += 1
            await self.update_status(AgentStatus.ERROR)
            
            return TaskResult(
                success=False,
                error=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def _analyze_objective_completion(self, objective: str, target: str, 
                                          previous_results: Dict[str, Any]) -> ObjectiveAnalysis:
        """Analizza il completamento dell'obiettivo"""
        analysis = ObjectiveAnalysis(objective)
        
        # Determina tipo di obiettivo
        objective_type = self._classify_objective(objective)
        
        # Ottieni criteri per questo tipo di obiettivo
        criteria = self.completion_criteria.get(objective_type, {})
        analysis.completion_criteria = list(criteria.keys())
        
        # Analizza risultati di ogni agente
        web_research = previous_results.get("a2_web_researcher", {})
        commands_data = previous_results.get("a3_command_generator", {})
        scripts_data = previous_results.get("a4_code_generator", {})
        execution_data = previous_results.get("a5_terminal_executor", {})
        
        # Calcola score per ogni criterio
        total_score = 0.0
        total_weight = 0.0
        
        for criterion, info in criteria.items():
            weight = info["weight"]
            met = await self._check_criterion(
                criterion, web_research, execution_data, target
            )
            
            if met:
                analysis.met_criteria.append(criterion)
                total_score += weight
                analysis.evidence.extend(met.get("evidence", []))
            else:
                analysis.missing_criteria.append(criterion)
            
            total_weight += weight
        
        # Calcola score finale
        analysis.completion_score = total_score / total_weight if total_weight > 0 else 0.0
        
        self.logger.info(f"Analisi completamento: {analysis.completion_score:.2f} ({len(analysis.met_criteria)}/{len(analysis.completion_criteria)} criteri)")
        
        return analysis
    
    def _classify_objective(self, objective: str) -> str:
        """Classifica il tipo di obiettivo"""
        objective_lower = objective.lower()
        
        if any(keyword in objective_lower for keyword in ["reconnaissance", "recon", "discovery", "enumeration"]):
            return "reconnaissance"
        elif any(keyword in objective_lower for keyword in ["vulnerability", "vuln", "scan", "assessment"]):
            return "vulnerability_assessment"
        elif any(keyword in objective_lower for keyword in ["penetration", "pentest", "exploit", "compromise"]):
            return "penetration_testing"
        elif any(keyword in objective_lower for keyword in ["web", "application", "webapp", "site"]):
            return "web_application_testing"
        else:
            return "reconnaissance"  # Default
    
    async def _check_criterion(self, criterion: str, web_research: Dict[str, Any], 
                             execution_data: Dict[str, Any], target: str) -> Optional[Dict[str, Any]]:
        """Verifica se un criterio è soddisfatto"""
        evidence = []
        
        if criterion == "port_scan_completed":
            # Controlla se ci sono risultati di scansione porte
            execution_results = execution_data.get("execution_results", [])
            for result in execution_results:
                if "nmap" in result.get("command", "").lower() and result.get("success"):
                    if self._contains_pattern(result.get("stdout_preview", ""), self.success_patterns["port_scan"]):
                        evidence.append(f"Scansione porte completata: {result.get('command', '')}")
                        return {"met": True, "evidence": evidence}
        
        elif criterion == "service_enumeration":
            # Controlla enumerazione servizi
            execution_results = execution_data.get("execution_results", [])
            for result in execution_results:
                command = result.get("command", "").lower()
                if any(tool in command for tool in ["nmap -sV", "enum4linux", "smbclient"]) and result.get("success"):
                    evidence.append(f"Enumerazione servizi: {result.get('command', '')}")
                    return {"met": True, "evidence": evidence}
        
        elif criterion == "web_enumeration":
            # Controlla enumerazione web
            execution_results = execution_data.get("execution_results", [])
            for result in execution_results:
                command = result.get("command", "").lower()
                if any(tool in command for tool in ["gobuster", "dirb", "nikto", "whatweb"]) and result.get("success"):
                    evidence.append(f"Enumerazione web: {result.get('command', '')}")
                    return {"met": True, "evidence": evidence}
        
        elif criterion == "subdomain_discovery":
            # Controlla scoperta subdomain
            subdomains = web_research.get("subdomains", [])
            execution_results = execution_data.get("execution_results", [])
            
            if subdomains:
                evidence.append(f"Subdomain trovati: {len(subdomains)}")
                return {"met": True, "evidence": evidence}
            
            for result in execution_results:
                command = result.get("command", "").lower()
                if any(tool in command for tool in ["subfinder", "amass", "assetfinder"]) and result.get("success"):
                    evidence.append(f"Ricerca subdomain: {result.get('command', '')}")
                    return {"met": True, "evidence": evidence}
        
        elif criterion == "technology_identification":
            # Controlla identificazione tecnologie
            technologies = web_research.get("technologies_identified", [])
            if technologies:
                evidence.append(f"Tecnologie identificate: {len(technologies)}")
                return {"met": True, "evidence": evidence}
        
        elif criterion == "vulnerability_scan":
            # Controlla scansione vulnerabilità
            vulnerabilities = web_research.get("vulnerabilities_found", [])
            execution_results = execution_data.get("execution_results", [])
            
            if vulnerabilities:
                evidence.append(f"Vulnerabilità trovate: {len(vulnerabilities)}")
                return {"met": True, "evidence": evidence}
            
            for result in execution_results:
                command = result.get("command", "").lower()
                if any(tool in command for tool in ["nikto", "sqlmap", "nuclei", "nessus"]) and result.get("success"):
                    if self._contains_pattern(result.get("stdout_preview", ""), self.success_patterns["vulnerability_found"]):
                        evidence.append(f"Scansione vulnerabilità: {result.get('command', '')}")
                        return {"met": True, "evidence": evidence}
        
        elif criterion == "initial_access":
            # Controlla accesso iniziale
            execution_results = execution_data.get("execution_results", [])
            for result in execution_results:
                stdout = result.get("stdout_preview", "")
                if self._contains_pattern(stdout, self.success_patterns["access_gained"]):
                    evidence.append(f"Accesso ottenuto: {result.get('command', '')}")
                    return {"met": True, "evidence": evidence}
        
        return None
    
    def _contains_pattern(self, text: str, patterns: List[str]) -> bool:
        """Verifica se il testo contiene uno dei pattern"""
        if not text:
            return False
        
        text_lower = text.lower()
        for pattern in patterns:
            if re.search(pattern.lower(), text_lower):
                return True
        return False
    
    async def _generate_recommendations(self, analysis: ObjectiveAnalysis, 
                                      previous_results: Dict[str, Any], 
                                      objective_reached: bool) -> List[str]:
        """Genera raccomandazioni per prossimi passi"""
        recommendations = []
        
        if objective_reached:
            recommendations.append("✅ Obiettivo raggiunto! Procedere con la generazione del report.")
            return recommendations
        
        # Raccomandazioni basate su criteri mancanti
        for missing in analysis.missing_criteria:
            if missing == "port_scan_completed":
                recommendations.append("🔍 Eseguire scansione porte completa con nmap")
            elif missing == "service_enumeration":
                recommendations.append("🔍 Enumerare servizi in dettaglio sui porte aperte")
            elif missing == "web_enumeration":
                recommendations.append("🌐 Eseguire enumerazione directory e file web")
            elif missing == "subdomain_discovery":
                recommendations.append("🔍 Ricercare subdomain del target")
            elif missing == "technology_identification":
                recommendations.append("🔧 Identificare tecnologie utilizzate dal target")
            elif missing == "vulnerability_scan":
                recommendations.append("🚨 Eseguire scansione vulnerabilità approfondita")
            elif missing == "initial_access":
                recommendations.append("🎯 Tentare di ottenere accesso iniziale al sistema")
        
        # Raccomandazioni basate su risultati precedenti
        execution_data = previous_results.get("a5_terminal_executor", {})
        failed_commands = [r for r in execution_data.get("execution_results", []) if not r.get("success")]
        
        if failed_commands:
            recommendations.append(f"⚠️ Riprovare {len(failed_commands)} comandi falliti con parametri diversi")
        
        # Raccomandazioni basate su score
        if analysis.completion_score < 0.3:
            recommendations.append("🔄 Score basso - ricominciare con reconnaissance più approfondita")
        elif analysis.completion_score < 0.6:
            recommendations.append("📈 Score medio - concentrarsi sui criteri mancanti più importanti")
        
        return recommendations
    
    async def _request_llm_analysis(self, objective: str, analysis: ObjectiveAnalysis, 
                                  previous_results: Dict[str, Any]) -> str:
        """Richiede analisi LLM tramite A1"""
        try:
            # Prepara prompt per analisi
            prompt = f"""
Analizza i risultati del penetration testing per l'obiettivo: {objective}

Score completamento: {analysis.completion_score:.2f}
Criteri soddisfatti: {len(analysis.met_criteria)}/{len(analysis.completion_criteria)}

Criteri mancanti: {', '.join(analysis.missing_criteria)}

Evidenze raccolte:
{chr(10).join(analysis.evidence[:5])}

Fornisci un'analisi dettagliata dello stato attuale e suggerimenti per completare l'obiettivo.
"""
            
            # Invia richiesta ad A1
            await self.send_message(
                receiver_id="a1_prompt_generator",
                message_type=MessageType.TASK_REQUEST,
                content={
                    "request_type": "llm_analysis",
                    "prompt": prompt,
                    "context": f"Analisi completamento obiettivo: {objective}",
                    "task_type": "analysis"
                },
                requires_response=True
            )
            
            # Per ora restituisce analisi base
            return f"Analisi automatica: Score {analysis.completion_score:.2f}, {len(analysis.met_criteria)} criteri soddisfatti"
            
        except Exception as e:
            self.logger.error(f"Errore richiesta analisi LLM: {e}")
            return "Analisi LLM non disponibile"
    
    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche specifiche di A6"""
        base_stats = super().get_stats()
        
        a6_stats = {
            "analyses_performed": len(self.analysis_history),
            "objectives_completed": len([a for a in self.analysis_history if a["objective_reached"]]),
            "completion_criteria_types": len(self.completion_criteria),
            "success_patterns_count": sum(len(patterns) for patterns in self.success_patterns.values())
        }
        
        base_stats.update(a6_stats)
        return base_stats
