"""
Agent Coordinator per il sistema di agenti collaborativi Heka
Coordina la comunicazione e l'esecuzione tra i 7 agenti specializzati
"""

import asyncio
import logging
import json
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from .base_agent import BaseAgent, AgentMessage, MessageType, AgentStatus, TaskResult

class CoordinationStrategy(Enum):
    SEQUENTIAL = "sequential"  # Agenti eseguono in sequenza
    PARALLEL = "parallel"     # Agenti eseguono in parallelo
    ADAPTIVE = "adaptive"     # Strategia adattiva basata sul task

@dataclass
class TaskExecution:
    """Esecuzione di un task nel sistema"""
    task_id: str
    objective: str
    target: str
    strategy: CoordinationStrategy
    current_agent: Optional[str] = None
    completed_agents: List[str] = field(default_factory=list)
    failed_agents: List[str] = field(default_factory=list)
    results: Dict[str, Any] = field(default_factory=dict)
    start_time: datetime = field(default_factory=datetime.now)
    status: str = "running"
    max_cycles: int = 10
    current_cycle: int = 0

class AgentCoordinator:
    """Coordinatore centrale per tutti gli agenti"""
    
    def __init__(self):
        self.logger = logging.getLogger("heka.coordinator")
        self.agents: Dict[str, BaseAgent] = {}
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.active_tasks: Dict[str, TaskExecution] = {}
        
        # Ordine di esecuzione degli agenti (A0 viene eseguito per primo e dopo ogni agente)
        self.agent_execution_order = [
            "a0_memory_manager",  # Inizializzazione memoria
            "a1_prompt_generator",
            "a2_web_researcher",
            "a3_command_generator",
            "a4_code_generator",
            "a5_terminal_executor",
            "a6_task_monitor",
            "a7_report_generator"
        ]

        # Dipendenze tra agenti (A0 non ha dipendenze, tutti dipendono da A0)
        self.agent_dependencies = {
            "a1_prompt_generator": ["a0_memory_manager"],
            "a2_web_researcher": ["a0_memory_manager", "a1_prompt_generator"],
            "a3_command_generator": ["a0_memory_manager", "a1_prompt_generator", "a2_web_researcher"],
            "a4_code_generator": ["a0_memory_manager", "a1_prompt_generator", "a2_web_researcher", "a3_command_generator"],
            "a5_terminal_executor": ["a0_memory_manager", "a3_command_generator", "a4_code_generator"],
            "a6_task_monitor": ["a0_memory_manager", "a2_web_researcher", "a3_command_generator", "a4_code_generator", "a5_terminal_executor"],
            "a7_report_generator": ["a0_memory_manager", "a6_task_monitor"]
        }
        
        self.running = False

    async def initialize_agents(self):
        """Inizializza tutti gli agenti del sistema"""
        try:
            # Importa e crea agenti
            from ..a0_memory_manager import A0MemoryManager
            from ..a1_prompt_generator import A1PromptGenerator
            from ..a2_web_researcher import A2WebResearcher
            from ..a3_command_generator import A3CommandGenerator
            from ..a4_code_generator import A4CodeGenerator
            from ..a5_terminal_executor import A5TerminalExecutor
            from ..a6_task_monitor import A6TaskMonitor
            from ..a7_report_generator import A7ReportGenerator

            # Crea e registra agenti (A0 per primo)
            a0 = A0MemoryManager()
            a1 = A1PromptGenerator()
            a2 = A2WebResearcher()
            a3 = A3CommandGenerator()
            a4 = A4CodeGenerator()
            a5 = A5TerminalExecutor()
            a6 = A6TaskMonitor()
            a7 = A7ReportGenerator()

            # Registra agenti (A0 per primo)
            self.register_agent(a0)
            self.register_agent(a1)
            self.register_agent(a2)
            self.register_agent(a3)
            self.register_agent(a4)
            self.register_agent(a5)
            self.register_agent(a6)
            self.register_agent(a7)

            # Inizializza agenti (A0 per primo per preparare la memoria)
            await a0.initialize()
            await a1.initialize()
            await a2.initialize()
            await a3.initialize()
            await a4.initialize()
            await a5.initialize()
            await a6.initialize()
            await a7.initialize()

            self.logger.info("Tutti gli 8 agenti (incluso A0 Memory Manager) inizializzati con successo")

        except Exception as e:
            self.logger.error(f"Errore inizializzazione agenti: {e}")
            raise

    def register_agent(self, agent: BaseAgent):
        """Registra un agente nel coordinatore"""
        self.agents[agent.agent_id] = agent
        agent.set_coordinator(self)
        self.logger.info(f"Agente registrato: {agent.name} ({agent.agent_id})")
    
    async def route_message(self, message: AgentMessage):
        """Instrada un messaggio tra agenti"""
        if message.receiver_id == "coordinator":
            # Messaggio per il coordinatore
            await self.message_queue.put(message)
        elif message.receiver_id in self.agents:
            # Messaggio per un agente specifico
            await self.agents[message.receiver_id].receive_message(message)
        else:
            self.logger.warning(f"Destinatario messaggio non trovato: {message.receiver_id}")
    
    async def broadcast_message(self, message_type: MessageType, content: Dict[str, Any], 
                              exclude_agents: List[str] = None):
        """Invia un messaggio a tutti gli agenti"""
        exclude_agents = exclude_agents or []
        
        for agent_id, agent in self.agents.items():
            if agent_id not in exclude_agents:
                message = AgentMessage(
                    sender_id="coordinator",
                    receiver_id=agent_id,
                    message_type=message_type,
                    content=content
                )
                await agent.receive_message(message)
    
    async def execute_collaborative_task(self, objective: str, target: str, 
                                       strategy: CoordinationStrategy = CoordinationStrategy.ADAPTIVE) -> Dict[str, Any]:
        """Esegue un task collaborativo utilizzando tutti gli agenti"""
        task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        task_execution = TaskExecution(
            task_id=task_id,
            objective=objective,
            target=target,
            strategy=strategy
        )
        
        self.active_tasks[task_id] = task_execution
        
        self.logger.info(f"Avvio task collaborativo: {task_id}")
        self.logger.info(f"Obiettivo: {objective}")
        self.logger.info(f"Target: {target}")
        self.logger.info(f"Strategia: {strategy.value}")
        
        try:
            if strategy == CoordinationStrategy.SEQUENTIAL:
                result = await self._execute_sequential(task_execution)
            elif strategy == CoordinationStrategy.PARALLEL:
                result = await self._execute_parallel(task_execution)
            else:  # ADAPTIVE
                result = await self._execute_adaptive(task_execution)
            
            task_execution.status = "completed"
            return result
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione task {task_id}: {e}")
            task_execution.status = "failed"
            return {"success": False, "error": str(e)}
        
        finally:
            # Cleanup
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
    
    async def _execute_sequential(self, task_execution: TaskExecution) -> Dict[str, Any]:
        """Esegue gli agenti in sequenza"""
        results = {}
        shared_context = {
            "objective": task_execution.objective,
            "target": task_execution.target,
            "results": {}
        }
        
        for agent_id in self.agent_execution_order:
            if agent_id not in self.agents:
                self.logger.warning(f"Agente {agent_id} non trovato, saltato")
                continue
            
            agent = self.agents[agent_id]
            task_execution.current_agent = agent_id
            
            self.logger.info(f"Esecuzione agente: {agent.name}")
            
            try:
                # Prepara dati task per l'agente
                task_data = {
                    "task_id": task_execution.task_id,
                    "objective": task_execution.objective,
                    "target": task_execution.target,
                    "shared_context": shared_context,
                    "previous_results": results
                }
                
                # Esegui task
                result = await agent.execute_task(task_data)

                if result.success:
                    results[agent_id] = result.data
                    shared_context["results"][agent_id] = result.data
                    task_execution.completed_agents.append(agent_id)

                    self.logger.info(f"Agente {agent.name} completato con successo")

                    # Se non è A0, invia i risultati ad A0 per memorizzazione
                    if agent_id != "a0_memory_manager" and "a0_memory_manager" in self.agents:
                        try:
                            a0_agent = self.agents["a0_memory_manager"]

                            # Esegui task di condivisione informazioni su A0
                            a0_task_data = {
                                "task_type": "information_sharing",
                                "objective": task_execution.objective,
                                "target": task_execution.target,
                                "shared_context": shared_context,
                                "previous_results": {agent_id: result.data}
                            }

                            a0_result = await a0_agent.execute_task(a0_task_data)

                            if a0_result.success:
                                # Aggiorna shared_context con informazioni correlate da A0
                                if "related_information" in a0_result.data:
                                    shared_context["memory_context"] = a0_result.data["related_information"]

                                self.logger.debug(f"Informazioni di {agent.name} memorizzate in A0")
                            else:
                                self.logger.warning(f"Errore memorizzazione A0: {a0_result.error}")

                        except Exception as e:
                            self.logger.error(f"Errore comunicazione con A0: {e}")
                    
                    # A6 controlla se l'obiettivo è raggiunto
                    if agent_id == "a6_task_monitor":
                        objective_reached = result.data.get("objective_reached", False)
                        if objective_reached:
                            self.logger.info("Obiettivo raggiunto, terminazione anticipata")
                            break
                        else:
                            # Se non raggiunto e non siamo al limite cicli, ricomincia
                            task_execution.current_cycle += 1
                            if task_execution.current_cycle < task_execution.max_cycles:
                                self.logger.info(f"Obiettivo non raggiunto, avvio ciclo {task_execution.current_cycle + 1}")
                                # Ricomincia dal primo agente (escluso A7)
                                continue
                            else:
                                self.logger.warning("Limite cicli raggiunto, procedo con report")
                
                else:
                    task_execution.failed_agents.append(agent_id)
                    self.logger.error(f"Agente {agent.name} fallito: {result.error}")
                    
                    # Continua con gli altri agenti anche in caso di fallimento
                    results[agent_id] = {"error": result.error}
                    
            except Exception as e:
                self.logger.error(f"Errore esecuzione agente {agent.name}: {e}")
                task_execution.failed_agents.append(agent_id)
                results[agent_id] = {"error": str(e)}
        
        return {
            "success": len(task_execution.completed_agents) > 0,
            "results": results,
            "completed_agents": task_execution.completed_agents,
            "failed_agents": task_execution.failed_agents,
            "cycles_executed": task_execution.current_cycle + 1
        }
    
    async def _execute_parallel(self, task_execution: TaskExecution) -> Dict[str, Any]:
        """Esegue gli agenti in parallelo (dove possibile)"""
        # Implementazione parallela basata su dipendenze
        # Per ora usa esecuzione sequenziale
        return await self._execute_sequential(task_execution)
    
    async def _execute_adaptive(self, task_execution: TaskExecution) -> Dict[str, Any]:
        """Esegue con strategia adattiva"""
        # Per ora usa esecuzione sequenziale
        return await self._execute_sequential(task_execution)
    
    async def start(self):
        """Avvia il coordinatore"""
        self.running = True
        self.logger.info("Coordinatore agenti avviato")
        
        # Avvia tutti gli agenti registrati
        for agent in self.agents.values():
            await agent.start()
        
        # Avvia processamento messaggi
        asyncio.create_task(self._process_coordinator_messages())
    
    async def stop(self):
        """Ferma il coordinatore"""
        self.running = False
        
        # Ferma tutti gli agenti
        for agent in self.agents.values():
            await agent.stop()
        
        self.logger.info("Coordinatore agenti fermato")
    
    async def _process_coordinator_messages(self):
        """Processa i messaggi diretti al coordinatore"""
        while self.running:
            try:
                message = await asyncio.wait_for(self.message_queue.get(), timeout=0.1)
                
                if message.message_type == MessageType.STATUS_UPDATE:
                    self._handle_agent_status_update(message)
                elif message.message_type == MessageType.ERROR_REPORT:
                    self._handle_agent_error(message)
                
                self.message_queue.task_done()
                
            except asyncio.TimeoutError:
                await asyncio.sleep(0.1)
            except Exception as e:
                self.logger.error(f"Errore processamento messaggio coordinatore: {e}")
                await asyncio.sleep(0.1)
    
    def _handle_agent_status_update(self, message: AgentMessage):
        """Gestisce aggiornamenti di stato degli agenti"""
        agent_id = message.sender_id
        content = message.content
        
        self.logger.debug(f"Aggiornamento stato {agent_id}: {content.get('new_status')}")
    
    def _handle_agent_error(self, message: AgentMessage):
        """Gestisce errori degli agenti"""
        agent_id = message.sender_id
        error = message.content.get('error', 'Unknown error')
        
        self.logger.error(f"Errore agente {agent_id}: {error}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Restituisce lo stato del sistema"""
        agent_stats = {}
        for agent_id, agent in self.agents.items():
            agent_stats[agent_id] = agent.get_stats()
        
        return {
            "coordinator_running": self.running,
            "registered_agents": len(self.agents),
            "active_tasks": len(self.active_tasks),
            "agent_stats": agent_stats
        }
