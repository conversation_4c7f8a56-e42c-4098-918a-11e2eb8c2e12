"""
Base Agent Class per il sistema di agenti collaborativi Heka
Classe base per tutti gli agenti del sistema
"""

import asyncio
import logging
import json
import uuid
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

class AgentStatus(Enum):
    IDLE = "idle"
    WORKING = "working"
    WAITING = "waiting"
    ERROR = "error"
    COMPLETED = "completed"

class MessageType(Enum):
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    STATUS_UPDATE = "status_update"
    ERROR_REPORT = "error_report"
    COORDINATION = "coordination"
    DATA_SHARE = "data_share"

@dataclass
class AgentMessage:
    """Messaggio tra agenti"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    receiver_id: str = ""
    message_type: MessageType = MessageType.COORDINATION
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    priority: int = 1  # 1=bassa, 5=alta
    requires_response: bool = False

@dataclass
class TaskResult:
    """Risultato di un task"""
    success: bool
    data: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None
    execution_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

class BaseAgent(ABC):
    """Classe base per tutti gli agenti del sistema"""
    
    def __init__(self, agent_id: str, name: str, description: str):
        self.agent_id = agent_id
        self.name = name
        self.description = description
        self.status = AgentStatus.IDLE
        self.logger = logging.getLogger(f"heka.agent.{agent_id}")
        
        # Sistema di messaggi
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.message_handlers: Dict[MessageType, Callable] = {}
        self.coordinator = None
        
        # Statistiche
        self.tasks_completed = 0
        self.tasks_failed = 0
        self.total_execution_time = 0.0
        self.start_time = datetime.now()
        
        # Configurazione
        self.max_retries = 3
        self.retry_delay = 1.0
        
        # Registra handlers di default
        self._register_default_handlers()
        
        self.logger.info(f"Agente {self.name} ({self.agent_id}) inizializzato")
    
    def _register_default_handlers(self):
        """Registra handlers di default per i messaggi"""
        self.message_handlers[MessageType.STATUS_UPDATE] = self._handle_status_update
        self.message_handlers[MessageType.ERROR_REPORT] = self._handle_error_report
        self.message_handlers[MessageType.COORDINATION] = self._handle_coordination
    
    async def _handle_status_update(self, message: AgentMessage):
        """Handler per aggiornamenti di stato"""
        self.logger.debug(f"Ricevuto aggiornamento stato da {message.sender_id}")
    
    async def _handle_error_report(self, message: AgentMessage):
        """Handler per report di errori"""
        self.logger.warning(f"Errore riportato da {message.sender_id}: {message.content.get('error', 'Unknown')}")
    
    async def _handle_coordination(self, message: AgentMessage):
        """Handler per messaggi di coordinamento"""
        self.logger.debug(f"Messaggio di coordinamento da {message.sender_id}")
    
    def set_coordinator(self, coordinator):
        """Imposta il coordinatore degli agenti"""
        self.coordinator = coordinator
    
    async def send_message(self, receiver_id: str, message_type: MessageType, 
                          content: Dict[str, Any], priority: int = 1, 
                          requires_response: bool = False) -> str:
        """Invia un messaggio ad un altro agente"""
        message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            content=content,
            priority=priority,
            requires_response=requires_response
        )
        
        if self.coordinator:
            await self.coordinator.route_message(message)
            self.logger.debug(f"Messaggio inviato a {receiver_id}: {message_type.value}")
            return message.id
        else:
            self.logger.error("Coordinatore non impostato, impossibile inviare messaggio")
            return ""
    
    async def receive_message(self, message: AgentMessage):
        """Riceve un messaggio da un altro agente"""
        await self.message_queue.put(message)
    
    async def process_messages(self):
        """Processa i messaggi in coda"""
        while True:
            try:
                # Timeout per evitare blocchi
                message = await asyncio.wait_for(self.message_queue.get(), timeout=0.1)
                
                # Trova handler appropriato
                handler = self.message_handlers.get(message.message_type)
                if handler:
                    await handler(message)
                else:
                    self.logger.warning(f"Nessun handler per tipo messaggio: {message.message_type.value}")
                
                # Marca messaggio come processato
                self.message_queue.task_done()
                
            except asyncio.TimeoutError:
                # Nessun messaggio in coda, continua
                await asyncio.sleep(0.1)
            except Exception as e:
                self.logger.error(f"Errore processamento messaggio: {e}")
                await asyncio.sleep(0.1)
    
    async def update_status(self, new_status: AgentStatus, details: str = ""):
        """Aggiorna lo stato dell'agente"""
        old_status = self.status
        self.status = new_status
        
        self.logger.info(f"Stato cambiato: {old_status.value} -> {new_status.value}")
        
        # Notifica coordinatore del cambio stato
        if self.coordinator:
            await self.send_message(
                receiver_id="coordinator",
                message_type=MessageType.STATUS_UPDATE,
                content={
                    "old_status": old_status.value,
                    "new_status": new_status.value,
                    "details": details
                }
            )
    
    @abstractmethod
    async def execute_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """Esegue un task specifico dell'agente"""
        pass
    
    @abstractmethod
    async def initialize(self):
        """Inizializza l'agente"""
        pass
    
    @abstractmethod
    async def shutdown(self):
        """Chiude l'agente"""
        pass
    
    async def start(self):
        """Avvia l'agente"""
        await self.initialize()
        await self.update_status(AgentStatus.IDLE)
        
        # Avvia task di processamento messaggi
        asyncio.create_task(self.process_messages())
        
        self.logger.info(f"Agente {self.name} avviato")
    
    async def stop(self):
        """Ferma l'agente"""
        await self.update_status(AgentStatus.COMPLETED)
        await self.shutdown()
        self.logger.info(f"Agente {self.name} fermato")
    
    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche dell'agente"""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status.value,
            "tasks_completed": self.tasks_completed,
            "tasks_failed": self.tasks_failed,
            "total_execution_time": self.total_execution_time,
            "uptime": uptime,
            "success_rate": self.tasks_completed / max(1, self.tasks_completed + self.tasks_failed)
        }
