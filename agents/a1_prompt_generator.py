"""
Agent A1 - Prompt Generator & LLM Interface
Responsabile della generazione di prompt e comunicazione con LLM tramite ai_client.py
Solo questo agente può inviare richieste a LLM
"""

import asyncio
import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

from .core.base_agent import BaseAgent, AgentStatus, TaskResult, MessageType
from .ai_client import AIClient, AIModel, AIRequest

class A1PromptGenerator(BaseAgent):
    """Agent A1 - Generatore di prompt e interfaccia LLM"""
    
    def __init__(self):
        super().__init__(
            agent_id="a1_prompt_generator",
            name="Prompt Generator & LLM Interface",
            description="Genera prompt ottimizzati e gestisce comunicazione con LLM"
        )
        
        self.ai_client: Optional[AIClient] = None
        self.prompt_templates = {}
        self.conversation_history = []
        self.max_history_length = 10
        
        # Contatori per rate limiting
        self.requests_sent = 0
        self.last_request_time = None
        self.min_request_interval = 0.5  # Minimo 500ms tra richieste
        
        # Cache per prompt frequenti
        self.prompt_cache = {}
        self.cache_max_size = 100
        
    async def initialize(self):
        """Inizializza l'agente A1"""
        try:
            # Importa e inizializza AI client
            from .ai_client import ai_client
            self.ai_client = ai_client
            
            if not self.ai_client.session:
                await self.ai_client.start()
            
            # Carica template di prompt
            await self._load_prompt_templates()
            
            self.logger.info("Agent A1 inizializzato con successo")
            
        except Exception as e:
            self.logger.error(f"Errore inizializzazione A1: {e}")
            raise
    
    async def shutdown(self):
        """Chiude l'agente A1"""
        if self.ai_client and self.ai_client.session:
            await self.ai_client.stop()
        
        self.logger.info("Agent A1 chiuso")
    
    async def _load_prompt_templates(self):
        """Carica i template di prompt per diversi tipi di task"""
        self.prompt_templates = {
            "web_research": {
                "system": "Sei un esperto ricercatore web specializzato in penetration testing e cybersecurity.",
                "user_template": "Ricerca informazioni su {target} per {objective}. Focus su: {focus_areas}"
            },
            "command_generation": {
                "system": "Sei un esperto di Kali Linux e penetration testing tools.",
                "user_template": "Genera comandi per {objective} su target {target}. Considera: {context}"
            },
            "code_generation": {
                "system": "Sei un esperto programmatore specializzato in security tools e automation.",
                "user_template": "Genera codice {language} per {objective}. Target: {target}. Requisiti: {requirements}"
            },
            "analysis": {
                "system": "Sei un esperto analista di cybersecurity e penetration testing.",
                "user_template": "Analizza i risultati: {results}. Obiettivo: {objective}. Fornisci insights e prossimi passi."
            },
            "report_generation": {
                "system": "Sei un esperto nella creazione di report di penetration testing professionali.",
                "user_template": "Crea un report dettagliato per {objective} su {target}. Risultati: {results}"
            }
        }
        
        self.logger.info(f"Caricati {len(self.prompt_templates)} template di prompt")
    
    async def execute_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """Esegue un task di generazione prompt e comunicazione LLM"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self.update_status(AgentStatus.WORKING)
            
            objective = task_data.get("objective", "")
            target = task_data.get("target", "")
            shared_context = task_data.get("shared_context", {})
            
            self.logger.info(f"A1 - Generazione prompt per obiettivo: {objective}")
            
            # Genera prompt per gli altri agenti
            prompts_generated = await self._generate_prompts_for_agents(
                objective, target, shared_context
            )
            
            # Esegui richiesta LLM per analisi iniziale
            initial_analysis = await self._perform_initial_analysis(
                objective, target, shared_context
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            self.total_execution_time += execution_time
            self.tasks_completed += 1
            
            result_data = {
                "prompts_generated": prompts_generated,
                "initial_analysis": initial_analysis,
                "llm_requests_made": 1,
                "execution_time": execution_time
            }
            
            await self.update_status(AgentStatus.IDLE)
            
            return TaskResult(
                success=True,
                data=result_data,
                execution_time=execution_time
            )
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione task A1: {e}")
            self.tasks_failed += 1
            await self.update_status(AgentStatus.ERROR)
            
            return TaskResult(
                success=False,
                error=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def _generate_prompts_for_agents(self, objective: str, target: str, 
                                         shared_context: Dict[str, Any]) -> Dict[str, str]:
        """Genera prompt ottimizzati per ogni agente"""
        prompts = {}
        
        # Prompt per A2 - Web Research
        prompts["a2_web_researcher"] = self._build_prompt(
            "web_research",
            target=target,
            objective=objective,
            focus_areas="vulnerabilità note, tecnologie utilizzate, superficie di attacco"
        )
        
        # Prompt per A3 - Command Generator
        prompts["a3_command_generator"] = self._build_prompt(
            "command_generation",
            target=target,
            objective=objective,
            context="reconnaissance iniziale, scanning porte, enumerazione servizi"
        )
        
        # Prompt per A4 - Code Generator
        prompts["a4_code_generator"] = self._build_prompt(
            "code_generation",
            language="python/bash",
            target=target,
            objective=objective,
            requirements="automation, parsing output, exploit development"
        )
        
        self.logger.info(f"Generati prompt per {len(prompts)} agenti")
        return prompts
    
    def _build_prompt(self, template_name: str, **kwargs) -> str:
        """Costruisce un prompt usando un template"""
        if template_name not in self.prompt_templates:
            return f"Esegui {template_name} per {kwargs}"
        
        template = self.prompt_templates[template_name]
        system_prompt = template["system"]
        user_prompt = template["user_template"].format(**kwargs)
        
        return f"SYSTEM: {system_prompt}\n\nUSER: {user_prompt}"
    
    async def _perform_initial_analysis(self, objective: str, target: str, 
                                      shared_context: Dict[str, Any]) -> str:
        """Esegue analisi iniziale tramite LLM"""
        prompt = self._build_prompt(
            "analysis",
            results="Inizio penetration testing",
            objective=objective
        )
        
        # Aggiungi contesto alla conversazione
        context = f"Target: {target}\nObiettivo: {objective}"
        
        try:
            response = await self.send_llm_request(prompt, context, "analysis")
            self.logger.info("Analisi iniziale completata")
            return response
            
        except Exception as e:
            self.logger.error(f"Errore analisi iniziale: {e}")
            return f"Errore analisi: {str(e)}"
    
    async def send_llm_request(self, prompt: str, context: str = "", 
                             task_type: str = "general") -> str:
        """Invia richiesta a LLM con rate limiting"""
        if not self.ai_client:
            raise Exception("AI Client non inizializzato")
        
        # Rate limiting
        await self._apply_rate_limiting()
        
        try:
            self.logger.debug(f"Invio richiesta LLM - Tipo: {task_type}")
            
            # Usa il metodo appropriato basato sul tipo di task
            if task_type == "code_generation":
                response = await self.ai_client.code_completion(
                    prompt=prompt,
                    context=context,
                    agent_type=self.agent_id
                )
            elif task_type == "analysis":
                response = await self.ai_client.analysis_completion(
                    prompt=prompt,
                    context=context,
                    agent_type=self.agent_id
                )
            else:
                response = await self.ai_client.chat_completion(
                    prompt=prompt,
                    context=context,
                    agent_type=self.agent_id,
                    task_type=task_type
                )
            
            # Aggiorna statistiche
            self.requests_sent += 1
            self.last_request_time = datetime.now()
            
            # Aggiungi alla cronologia
            self._add_to_history(prompt, response)
            
            self.logger.debug(f"Risposta LLM ricevuta ({len(response)} caratteri)")
            return response
            
        except Exception as e:
            self.logger.error(f"Errore richiesta LLM: {e}")
            raise
    
    async def _apply_rate_limiting(self):
        """Applica rate limiting per evitare sovraccarico LLM"""
        if self.last_request_time:
            time_since_last = (datetime.now() - self.last_request_time).total_seconds()
            if time_since_last < self.min_request_interval:
                wait_time = self.min_request_interval - time_since_last
                self.logger.debug(f"Rate limiting: attesa {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
    
    def _add_to_history(self, prompt: str, response: str):
        """Aggiunge interazione alla cronologia"""
        self.conversation_history.append({
            "timestamp": datetime.now(),
            "prompt": prompt[:200] + "..." if len(prompt) > 200 else prompt,
            "response": response[:200] + "..." if len(response) > 200 else response
        })
        
        # Mantieni solo le ultime N interazioni
        if len(self.conversation_history) > self.max_history_length:
            self.conversation_history = self.conversation_history[-self.max_history_length:]
    
    async def handle_llm_request_from_agent(self, requesting_agent: str, 
                                          prompt: str, context: str = "", 
                                          task_type: str = "general") -> str:
        """Gestisce richieste LLM da altri agenti"""
        self.logger.info(f"Richiesta LLM da {requesting_agent}")
        
        # Solo A1 può fare richieste LLM
        try:
            response = await self.send_llm_request(prompt, context, task_type)
            
            # Invia risposta all'agente richiedente
            await self.send_message(
                receiver_id=requesting_agent,
                message_type=MessageType.TASK_RESPONSE,
                content={
                    "llm_response": response,
                    "request_id": f"llm_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                }
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Errore gestione richiesta LLM: {e}")
            
            # Invia errore all'agente richiedente
            await self.send_message(
                receiver_id=requesting_agent,
                message_type=MessageType.ERROR_REPORT,
                content={
                    "error": str(e),
                    "request_failed": True
                }
            )
            
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche specifiche di A1"""
        base_stats = super().get_stats()
        
        a1_stats = {
            "llm_requests_sent": self.requests_sent,
            "conversation_history_length": len(self.conversation_history),
            "prompt_templates_loaded": len(self.prompt_templates),
            "cache_size": len(self.prompt_cache)
        }
        
        base_stats.update(a1_stats)
        return base_stats
