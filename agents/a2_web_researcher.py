"""
Agent A2 - Web Research & Navigation
Responsabile della ricerca e navigazione web autonoma attraverso Playwright
"""

import asyncio
import logging
import json
import re
from typing import Dict, Any, Optional, List
from datetime import datetime
from urllib.parse import urljoin, urlparse

from .core.base_agent import BaseAgent, AgentStatus, TaskResult, MessageType

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

class A2WebResearcher(BaseAgent):
    """Agent A2 - Ricercatore e navigatore web"""
    
    def __init__(self):
        super().__init__(
            agent_id="a2_web_researcher",
            name="Web Research & Navigation",
            description="Ricerca autonoma e navigazione web per intelligence gathering"
        )
        
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        
        # Configurazione ricerca
        self.search_engines = {
            "bing": "https://www.bing.com/search?q={}",
            "duckduckgo": "https://duckduckgo.com/?q={}",
            "shodan": "https://www.shodan.io/search?query={}",
            "crtsh": "https://crt.sh/?q={}",
            "intelx": "https://intelx.io/?s={}",
            "greynoise": "https://viz.greynoise.io/ip/{}",
            "wayback": "https://web.archive.org/web/*/{}*"
        }
        
        # Search dorks per penetration testing
        self.security_dorks = [
            'site:{} filetype:pdf',
            'site:{} inurl:admin',
            'site:{} inurl:login',
            'site:{} inurl:config',
            'site:{} "index of"',
            'site:{} intext:"password"',
            'site:{} inurl:wp-admin',
            'site:{} inurl:phpmyadmin',
            'site:{} filetype:sql',
            'site:{} filetype:log'
        ]

        # Shodan search queries per penetration testing
        self.shodan_queries = [
            'hostname:{}',
            'ip:{}',
            'port:22 {}',
            'port:80 {}',
            'port:443 {}',
            'port:21 {}',
            'port:23 {}',
            'port:3389 {}',
            'apache {}',
            'nginx {}',
            'iis {}',
            'ssh {}',
            'ftp {}',
            'telnet {}'
        ]

        # Certificate Transparency queries per crt.sh
        self.crtsh_queries = [
            '{}',
            '%.{}',
            'www.{}',
            'mail.{}',
            'ftp.{}',
            'api.{}',
            'admin.{}'
        ]

        # Intelligence X queries
        self.intelx_queries = [
            '{}',
            'domain:{}',
            'email:{}',
            'ip:{}'
        ]

        # GreyNoise queries (principalmente per IP)
        self.greynoise_queries = [
            '{}'  # Solo IP per GreyNoise
        ]

        # Wayback Machine queries
        self.wayback_queries = [
            '{}',
            'http://{}',
            'https://{}',
            'www.{}'
        ]
        
        # Risultati ricerca
        self.research_results = {}
        self.visited_urls = set()
        self.max_pages_per_search = 5
        self.max_search_results = 20
        
    async def initialize(self):
        """Inizializza l'agente A2"""
        if not PLAYWRIGHT_AVAILABLE:
            raise Exception("Playwright non disponibile. Installare con: pip install playwright")
        
        try:
            self.playwright = await async_playwright().start()
            
            # Avvia browser in modalità headless
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # Crea contesto con user agent realistico
            self.context = await self.browser.new_context(
                user_agent='Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                viewport={'width': 1920, 'height': 1080}
            )
            
            # Crea pagina principale
            self.page = await self.context.new_page()
            
            # Imposta timeout
            self.page.set_default_timeout(30000)  # 30 secondi
            
            self.logger.info("Agent A2 inizializzato con Playwright")
            
        except Exception as e:
            self.logger.error(f"Errore inizializzazione A2: {e}")
            raise
    
    async def shutdown(self):
        """Chiude l'agente A2"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
                
            self.logger.info("Agent A2 chiuso")
            
        except Exception as e:
            self.logger.error(f"Errore chiusura A2: {e}")
    
    async def execute_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """Esegue un task di ricerca web"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            await self.update_status(AgentStatus.WORKING)
            
            objective = task_data.get("objective", "")
            target = task_data.get("target", "")
            shared_context = task_data.get("shared_context", {})
            
            self.logger.info(f"A2 - Ricerca web per target: {target}")
            
            # Ottieni prompt da A1 se disponibile
            prompts = shared_context.get("results", {}).get("a1_prompt_generator", {}).get("prompts_generated", {})
            research_prompt = prompts.get("a2_web_researcher", "")
            
            # Esegui ricerca completa
            research_results = await self._perform_comprehensive_research(
                target, objective, research_prompt
            )
            
            execution_time = asyncio.get_event_loop().time() - start_time
            self.total_execution_time += execution_time
            self.tasks_completed += 1
            
            result_data = {
                "research_results": research_results,
                "urls_visited": len(self.visited_urls),
                "searches_performed": len(research_results.get("searches", [])),
                "vulnerabilities_found": research_results.get("vulnerabilities", []),
                "technologies_identified": research_results.get("technologies", []),
                "execution_time": execution_time
            }
            
            await self.update_status(AgentStatus.IDLE)
            
            return TaskResult(
                success=True,
                data=result_data,
                execution_time=execution_time
            )
            
        except Exception as e:
            self.logger.error(f"Errore esecuzione task A2: {e}")
            self.tasks_failed += 1
            await self.update_status(AgentStatus.ERROR)
            
            return TaskResult(
                success=False,
                error=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def _perform_comprehensive_research(self, target: str, objective: str, 
                                            research_prompt: str) -> Dict[str, Any]:
        """Esegue ricerca completa su un target"""
        results = {
            "target": target,
            "objective": objective,
            "searches": [],
            "vulnerabilities": [],
            "technologies": [],
            "interesting_urls": [],
            "social_media": [],
            "subdomains": [],
            "emails": [],
            "phone_numbers": []
        }
        
        # 1. Ricerca generale sul target
        general_results = await self._search_target_general(target)
        results["searches"].append({"type": "general", "results": general_results})
        
        # 2. Ricerca vulnerabilità con dorks
        vuln_results = await self._search_vulnerabilities_with_dorks(target)
        results["searches"].append({"type": "vulnerabilities", "results": vuln_results})
        results["vulnerabilities"].extend(vuln_results.get("vulnerabilities", []))
        
        # 3. Ricerca tecnologie
        tech_results = await self._identify_technologies(target)
        results["technologies"].extend(tech_results)
        
        # 4. Ricerca subdomain
        subdomain_results = await self._search_subdomains(target)
        results["subdomains"].extend(subdomain_results)
        
        # 5. Ricerca social media e contatti
        social_results = await self._search_social_media(target)
        results["social_media"].extend(social_results.get("social_media", []))
        results["emails"].extend(social_results.get("emails", []))
        results["phone_numbers"].extend(social_results.get("phone_numbers", []))
        
        self.logger.info(f"Ricerca completata: {len(results['searches'])} ricerche eseguite")
        return results
    
    async def _search_target_general(self, target: str) -> Dict[str, Any]:
        """Ricerca generale informazioni sul target"""
        results = {
            "urls": [],
            "descriptions": [],
            "related_domains": [],
            "shodan_results": [],
            "crtsh_results": [],
            "intelx_results": [],
            "greynoise_results": [],
            "wayback_results": []
        }

        try:
            # Ricerca su DuckDuckGo per evitare tracking
            search_query = f'"{target}" OR site:{target}'
            duckduckgo_results = await self._perform_search("duckduckgo", search_query)
            results["urls"].extend(duckduckgo_results.get("urls", []))

            # Ricerca su Bing
            bing_results = await self._perform_search("bing", search_query)
            results["urls"].extend(bing_results.get("urls", []))

            # Ricerca su Shodan per informazioni tecniche
            await self._search_shodan_target(target, results)

            # Ricerca su crt.sh per certificati SSL
            await self._search_crtsh_target(target, results)

            # Ricerca su Intelligence X
            await self._search_intelx_target(target, results)

            # Ricerca su GreyNoise (solo se target è un IP)
            if self._is_ip_address(target):
                await self._search_greynoise_target(target, results)

            # Ricerca su Wayback Machine
            await self._search_wayback_target(target, results)

            # Rimuovi duplicati
            results["urls"] = list(set(results["urls"]))

            self.logger.info(f"Ricerca generale: {len(results['urls'])} URL trovati")

        except Exception as e:
            self.logger.error(f"Errore ricerca generale: {e}")

        return results

    async def _search_shodan_target(self, target: str, results: Dict[str, Any]) -> None:
        """Ricerca informazioni su Shodan per il target"""
        try:
            for query_template in self.shodan_queries:
                query = query_template.format(target)
                self.logger.debug(f"Ricerca Shodan: {query}")

                # Esegui ricerca su Shodan
                shodan_results = await self._perform_search("shodan", query)

                if shodan_results.get("urls"):
                    results["shodan_results"].append({
                        "query": query,
                        "results": shodan_results
                    })

                # Pausa per evitare rate limiting
                await asyncio.sleep(1)

        except Exception as e:
            self.logger.error(f"Errore ricerca Shodan: {e}")

    async def _search_crtsh_target(self, target: str, results: Dict[str, Any]) -> None:
        """Ricerca certificati SSL su crt.sh"""
        try:
            for query_template in self.crtsh_queries:
                query = query_template.format(target)
                self.logger.debug(f"Ricerca crt.sh: {query}")

                crtsh_results = await self._perform_search("crtsh", query)

                if crtsh_results.get("urls"):
                    results["crtsh_results"].append({
                        "query": query,
                        "results": crtsh_results
                    })

                await asyncio.sleep(1)

        except Exception as e:
            self.logger.error(f"Errore ricerca crt.sh: {e}")

    async def _search_intelx_target(self, target: str, results: Dict[str, Any]) -> None:
        """Ricerca su Intelligence X"""
        try:
            for query_template in self.intelx_queries:
                query = query_template.format(target)
                self.logger.debug(f"Ricerca IntelX: {query}")

                intelx_results = await self._perform_search("intelx", query)

                if intelx_results.get("urls"):
                    results["intelx_results"].append({
                        "query": query,
                        "results": intelx_results
                    })

                await asyncio.sleep(1)

        except Exception as e:
            self.logger.error(f"Errore ricerca IntelX: {e}")

    async def _search_greynoise_target(self, target: str, results: Dict[str, Any]) -> None:
        """Ricerca su GreyNoise (solo per IP)"""
        try:
            query = target  # GreyNoise usa direttamente l'IP
            self.logger.debug(f"Ricerca GreyNoise: {query}")

            greynoise_results = await self._perform_search("greynoise", query)

            if greynoise_results.get("urls"):
                results["greynoise_results"].append({
                    "query": query,
                    "results": greynoise_results
                })

        except Exception as e:
            self.logger.error(f"Errore ricerca GreyNoise: {e}")

    async def _search_wayback_target(self, target: str, results: Dict[str, Any]) -> None:
        """Ricerca su Wayback Machine"""
        try:
            for query_template in self.wayback_queries:
                query = query_template.format(target)
                self.logger.debug(f"Ricerca Wayback: {query}")

                wayback_results = await self._perform_search("wayback", query)

                if wayback_results.get("urls"):
                    results["wayback_results"].append({
                        "query": query,
                        "results": wayback_results
                    })

                await asyncio.sleep(1)

        except Exception as e:
            self.logger.error(f"Errore ricerca Wayback: {e}")

    def _is_ip_address(self, target: str) -> bool:
        """Verifica se il target è un indirizzo IP"""
        import re
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        return bool(re.match(ip_pattern, target))

    async def _search_vulnerabilities_with_dorks(self, target: str) -> Dict[str, Any]:
        """Ricerca vulnerabilità usando search dorks"""
        results = {"vulnerabilities": [], "sensitive_files": [], "admin_panels": []}

        try:
            for dork_template in self.security_dorks:
                dork = dork_template.format(target)

                self.logger.debug(f"Esecuzione dork: {dork}")

                # Prova prima DuckDuckGo, poi Bing
                search_results = await self._perform_search("duckduckgo", dork)
                if not search_results.get("urls"):
                    search_results = await self._perform_search("bing", dork)

                # Analizza risultati per tipo
                for url in search_results.get("urls", []):
                    if any(keyword in url.lower() for keyword in ["admin", "login", "wp-admin"]):
                        results["admin_panels"].append(url)
                    elif any(ext in url.lower() for ext in [".sql", ".log", ".config", ".pdf"]):
                        results["sensitive_files"].append(url)
                    else:
                        results["vulnerabilities"].append({
                            "url": url,
                            "dork_used": dork,
                            "potential_issue": self._classify_vulnerability(url)
                        })

                # Pausa tra dorks per evitare rate limiting
                await asyncio.sleep(2)
            
            self.logger.info(f"Dork search: {len(results['vulnerabilities'])} potenziali vulnerabilità")
            
        except Exception as e:
            self.logger.error(f"Errore ricerca dorks: {e}")
        
        return results
    
    def _classify_vulnerability(self, url: str) -> str:
        """Classifica il tipo di vulnerabilità basato sull'URL"""
        url_lower = url.lower()
        
        if "admin" in url_lower or "login" in url_lower:
            return "Admin Panel Exposed"
        elif "config" in url_lower:
            return "Configuration File Exposed"
        elif "index of" in url_lower:
            return "Directory Listing"
        elif "password" in url_lower:
            return "Password Information Leak"
        elif ".sql" in url_lower:
            return "Database File Exposed"
        elif ".log" in url_lower:
            return "Log File Exposed"
        else:
            return "Information Disclosure"
    
    async def _identify_technologies(self, target: str) -> List[str]:
        """Identifica tecnologie utilizzate dal target"""
        technologies = []
        
        try:
            # Visita il sito principale per analisi headers e contenuto
            if not target.startswith("http"):
                target_url = f"https://{target}"
            else:
                target_url = target
            
            response = await self.page.goto(target_url, wait_until="networkidle")
            
            if response:
                # Analizza headers
                headers = response.headers
                
                # Server web
                if "server" in headers:
                    technologies.append(f"Server: {headers['server']}")
                
                # Framework detection
                if "x-powered-by" in headers:
                    technologies.append(f"Framework: {headers['x-powered-by']}")
                
                # Analizza contenuto HTML
                content = await self.page.content()
                
                # WordPress
                if "wp-content" in content or "wordpress" in content.lower():
                    technologies.append("CMS: WordPress")
                
                # Joomla
                if "joomla" in content.lower():
                    technologies.append("CMS: Joomla")
                
                # Drupal
                if "drupal" in content.lower():
                    technologies.append("CMS: Drupal")
                
                # JavaScript frameworks
                if "react" in content.lower():
                    technologies.append("Frontend: React")
                if "angular" in content.lower():
                    technologies.append("Frontend: Angular")
                if "vue" in content.lower():
                    technologies.append("Frontend: Vue.js")
            
            self.logger.info(f"Tecnologie identificate: {len(technologies)}")
            
        except Exception as e:
            self.logger.error(f"Errore identificazione tecnologie: {e}")
        
        return technologies
    
    async def _search_subdomains(self, target: str) -> List[str]:
        """Ricerca subdomain del target"""
        subdomains = []
        
        try:
            # Ricerca subdomain con motori di ricerca alternativi
            subdomain_dorks = [
                f'site:*.{target}',
                f'site:{target} -www',
                f'inurl:{target} subdomain'
            ]

            for dork in subdomain_dorks:
                # Prova DuckDuckGo prima, poi Bing
                results = await self._perform_search("duckduckgo", dork)
                if not results.get("urls"):
                    results = await self._perform_search("bing", dork)

                for url in results.get("urls", []):
                    # Estrai subdomain dall'URL
                    parsed = urlparse(url)
                    if parsed.hostname and target in parsed.hostname:
                        subdomains.append(parsed.hostname)

                await asyncio.sleep(1)

            # Ricerca anche su Shodan per subdomain
            shodan_subdomain_query = f'hostname:*.{target}'
            shodan_results = await self._perform_search("shodan", shodan_subdomain_query)
            for url in shodan_results.get("urls", []):
                parsed = urlparse(url)
                if parsed.hostname and target in parsed.hostname:
                    subdomains.append(parsed.hostname)
            
            # Rimuovi duplicati
            subdomains = list(set(subdomains))
            
            self.logger.info(f"Subdomain trovati: {len(subdomains)}")
            
        except Exception as e:
            self.logger.error(f"Errore ricerca subdomain: {e}")
        
        return subdomains
    
    async def _search_social_media(self, target: str) -> Dict[str, List]:
        """Ricerca presenza social media e contatti"""
        results = {"social_media": [], "emails": [], "phone_numbers": []}
        
        try:
            # Ricerca social media
            social_queries = [
                f'"{target}" site:linkedin.com',
                f'"{target}" site:twitter.com',
                f'"{target}" site:facebook.com',
                f'"{target}" site:instagram.com'
            ]
            
            for query in social_queries:
                search_results = await self._perform_search("google", query)
                results["social_media"].extend(search_results.get("urls", []))
                await asyncio.sleep(1)
            
            # Ricerca email e telefoni nel sito principale
            if not target.startswith("http"):
                target_url = f"https://{target}"
            else:
                target_url = target
            
            try:
                await self.page.goto(target_url)
                content = await self.page.content()
                
                # Regex per email
                email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
                emails = re.findall(email_pattern, content)
                results["emails"].extend(emails)
                
                # Regex per telefoni
                phone_pattern = r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
                phones = re.findall(phone_pattern, content)
                results["phone_numbers"].extend([phone[0] + phone[1] if isinstance(phone, tuple) else phone for phone in phones])
                
            except Exception as e:
                self.logger.debug(f"Errore analisi contenuto sito: {e}")
            
            self.logger.info(f"Social media: {len(results['social_media'])}, Email: {len(results['emails'])}")
            
        except Exception as e:
            self.logger.error(f"Errore ricerca social media: {e}")
        
        return results
    
    async def _perform_search(self, engine: str, query: str) -> Dict[str, Any]:
        """Esegue una ricerca su un motore specifico"""
        results = {"urls": [], "titles": [], "descriptions": []}
        
        try:
            if engine not in self.search_engines:
                raise ValueError(f"Motore di ricerca non supportato: {engine}")
            
            search_url = self.search_engines[engine].format(query.replace(" ", "+"))
            
            self.logger.debug(f"Ricerca {engine}: {query}")
            
            await self.page.goto(search_url, wait_until="networkidle")
            
            # Attendi caricamento risultati
            await asyncio.sleep(2)
            
            # Estrai risultati basato sul motore
            if engine == "bing":
                results = await self._extract_bing_results()
            elif engine == "duckduckgo":
                results = await self._extract_duckduckgo_results()
            elif engine == "shodan":
                results = await self._extract_shodan_results()
            elif engine == "crtsh":
                results = await self._extract_crtsh_results()
            elif engine == "intelx":
                results = await self._extract_intelx_results()
            elif engine == "greynoise":
                results = await self._extract_greynoise_results()
            elif engine == "wayback":
                results = await self._extract_wayback_results()
            
            # Limita numero risultati
            results["urls"] = results["urls"][:self.max_search_results]
            
            self.logger.debug(f"Trovati {len(results['urls'])} risultati")
            
        except Exception as e:
            self.logger.error(f"Errore ricerca {engine}: {e}")
        
        return results
    

    async def _extract_bing_results(self) -> Dict[str, Any]:
        """Estrae risultati da Bing"""
        results = {"urls": [], "titles": [], "descriptions": []}
        
        try:
            result_links = await self.page.query_selector_all('.b_algo h2 a')
            
            for link in result_links[:self.max_search_results]:
                href = await link.get_attribute('href')
                if href and href.startswith('http'):
                    results["urls"].append(href)
                    
                    title = await link.text_content()
                    if title:
                        results["titles"].append(title)
            
        except Exception as e:
            self.logger.debug(f"Errore estrazione risultati Bing: {e}")
        
        return results
    
    async def _extract_duckduckgo_results(self) -> Dict[str, Any]:
        """Estrae risultati da DuckDuckGo"""
        results = {"urls": [], "titles": [], "descriptions": []}
        
        try:
            result_links = await self.page.query_selector_all('.result__a')
            
            for link in result_links[:self.max_search_results]:
                href = await link.get_attribute('href')
                if href and href.startswith('http'):
                    results["urls"].append(href)
                    
                    title = await link.text_content()
                    if title:
                        results["titles"].append(title)
            
        except Exception as e:
            self.logger.debug(f"Errore estrazione risultati DuckDuckGo: {e}")
        
        return results

    async def _extract_shodan_results(self) -> Dict[str, Any]:
        """Estrae risultati da Shodan"""
        results = {"urls": [], "titles": [], "descriptions": []}

        try:
            # Attendi caricamento pagina Shodan
            await asyncio.sleep(3)

            # Selettori per risultati Shodan
            result_selectors = [
                '.result',
                '.search-result',
                '.result-item'
            ]

            for selector in result_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    if elements:
                        for element in elements[:self.max_search_results]:
                            try:
                                # Estrai informazioni dal risultato
                                title_elem = await element.query_selector('h3, .title, .result-title')
                                desc_elem = await element.query_selector('.description, .result-description, p')
                                link_elem = await element.query_selector('a')

                                title = await title_elem.inner_text() if title_elem else ""
                                description = await desc_elem.inner_text() if desc_elem else ""
                                url = await link_elem.get_attribute('href') if link_elem else ""

                                if url and not url.startswith('http'):
                                    url = f"https://www.shodan.io{url}"

                                if title and url:
                                    results["titles"].append(title.strip())
                                    results["descriptions"].append(description.strip())
                                    results["urls"].append(url)

                            except Exception as e:
                                self.logger.debug(f"Errore estrazione singolo risultato Shodan: {e}")
                        break

                except Exception as e:
                    self.logger.debug(f"Errore con selettore {selector}: {e}")
                    continue

            self.logger.debug(f"Shodan: {len(results['urls'])} risultati estratti")

        except Exception as e:
            self.logger.debug(f"Errore estrazione risultati Shodan: {e}")

        return results

    async def _extract_crtsh_results(self) -> Dict[str, Any]:
        """Estrae risultati da crt.sh"""
        results = {"urls": [], "titles": [], "descriptions": []}

        try:
            await asyncio.sleep(2)

            # Selettori per crt.sh
            table_rows = await self.page.query_selector_all('table tr')

            for row in table_rows[:self.max_search_results]:
                try:
                    cells = await row.query_selector_all('td')
                    if len(cells) >= 2:
                        # Estrai informazioni dal certificato
                        domain_cell = cells[1] if len(cells) > 1 else None
                        if domain_cell:
                            domain_text = await domain_cell.inner_text()
                            if domain_text and domain_text.strip():
                                results["titles"].append(f"Certificate: {domain_text.strip()}")
                                results["descriptions"].append("SSL Certificate from crt.sh")
                                results["urls"].append(f"https://crt.sh/?q={domain_text.strip()}")

                except Exception as e:
                    self.logger.debug(f"Errore estrazione riga crt.sh: {e}")

            self.logger.debug(f"crt.sh: {len(results['urls'])} risultati estratti")

        except Exception as e:
            self.logger.debug(f"Errore estrazione risultati crt.sh: {e}")

        return results

    async def _extract_intelx_results(self) -> Dict[str, Any]:
        """Estrae risultati da Intelligence X"""
        results = {"urls": [], "titles": [], "descriptions": []}

        try:
            await asyncio.sleep(3)

            # Selettori per IntelX
            result_selectors = [
                '.result-item',
                '.search-result',
                '.result'
            ]

            for selector in result_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    if elements:
                        for element in elements[:self.max_search_results]:
                            try:
                                title_elem = await element.query_selector('h3, .title, .result-title')
                                desc_elem = await element.query_selector('.description, p')
                                link_elem = await element.query_selector('a')

                                title = await title_elem.inner_text() if title_elem else ""
                                description = await desc_elem.inner_text() if desc_elem else ""
                                url = await link_elem.get_attribute('href') if link_elem else ""

                                if title and url:
                                    results["titles"].append(title.strip())
                                    results["descriptions"].append(description.strip())
                                    results["urls"].append(url)

                            except Exception as e:
                                self.logger.debug(f"Errore estrazione singolo risultato IntelX: {e}")
                        break

                except Exception as e:
                    continue

            self.logger.debug(f"IntelX: {len(results['urls'])} risultati estratti")

        except Exception as e:
            self.logger.debug(f"Errore estrazione risultati IntelX: {e}")

        return results

    async def _extract_greynoise_results(self) -> Dict[str, Any]:
        """Estrae risultati da GreyNoise"""
        results = {"urls": [], "titles": [], "descriptions": []}

        try:
            await asyncio.sleep(3)

            # Cerca informazioni IP su GreyNoise
            ip_info = await self.page.query_selector('.ip-info, .result-info')
            if ip_info:
                info_text = await ip_info.inner_text()
                if info_text:
                    results["titles"].append("GreyNoise IP Information")
                    results["descriptions"].append(info_text.strip())
                    results["urls"].append(self.page.url)

            self.logger.debug(f"GreyNoise: {len(results['urls'])} risultati estratti")

        except Exception as e:
            self.logger.debug(f"Errore estrazione risultati GreyNoise: {e}")

        return results

    async def _extract_wayback_results(self) -> Dict[str, Any]:
        """Estrae risultati da Wayback Machine"""
        results = {"urls": [], "titles": [], "descriptions": []}

        try:
            await asyncio.sleep(3)

            # Selettori per Wayback Machine
            snapshot_links = await self.page.query_selector_all('.snapshot a, .calendar a')

            for link in snapshot_links[:self.max_search_results]:
                try:
                    href = await link.get_attribute('href')
                    text = await link.inner_text()

                    if href and text:
                        results["urls"].append(f"https://web.archive.org{href}" if href.startswith('/') else href)
                        results["titles"].append(f"Wayback: {text.strip()}")
                        results["descriptions"].append("Archived page from Wayback Machine")

                except Exception as e:
                    self.logger.debug(f"Errore estrazione link Wayback: {e}")

            self.logger.debug(f"Wayback: {len(results['urls'])} risultati estratti")

        except Exception as e:
            self.logger.debug(f"Errore estrazione risultati Wayback: {e}")

        return results

    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche specifiche di A2"""
        base_stats = super().get_stats()
        
        a2_stats = {
            "urls_visited": len(self.visited_urls),
            "research_results_count": len(self.research_results),
            "search_engines_available": len(self.search_engines),
            "security_dorks_count": len(self.security_dorks),
            "shodan_queries_count": len(self.shodan_queries),
            "crtsh_queries_count": len(self.crtsh_queries),
            "intelx_queries_count": len(self.intelx_queries),
            "greynoise_queries_count": len(self.greynoise_queries),
            "wayback_queries_count": len(self.wayback_queries)
        }
        
        base_stats.update(a2_stats)
        return base_stats
