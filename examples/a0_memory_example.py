#!/usr/bin/env python3
"""
Esempio di utilizzo dell'agente A0 Memory Manager
Dimostra come memorizzare, recuperare e correlare informazioni
"""

import asyncio
import sys
import os

# Aggiungi il path del progetto
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.a0_memory_manager import A0MemoryManager, InformationType, InformationPriority
from datetime import datetime, timedelta

async def demo_basic_operations():
    """Dimostra operazioni base di A0"""
    print("🧠 Demo A0 Memory Manager - Operazioni Base")
    print("=" * 50)
    
    # Inizializza A0
    a0 = A0MemoryManager()
    await a0.initialize()
    
    # 1. Memorizza informazioni su un target
    print("\n1. Memorizzazione informazioni target...")
    target_info_id = await a0.store_information(
        source_agent="demo",
        info_type=InformationType.TARGET_INFO,
        content={
            "target": "*************",
            "hostname": "target-server",
            "os": "Linux Ubuntu 20.04",
            "open_ports": [22, 80, 443, 3306],
            "services": {
                "22": "SSH OpenSSH 8.2",
                "80": "Apache 2.4.41",
                "443": "Apache 2.4.41 SSL",
                "3306": "MySQL 8.0"
            }
        },
        priority=InformationPriority.HIGH,
        tags={"*************", "ubuntu", "web-server", "mysql"},
        metadata={
            "scan_date": datetime.now().isoformat(),
            "scanner": "nmap",
            "confidence": "high"
        }
    )
    print(f"✅ Target info memorizzata: {target_info_id}")
    
    # 2. Memorizza vulnerabilità trovate
    print("\n2. Memorizzazione vulnerabilità...")
    vuln_id = await a0.store_information(
        source_agent="demo",
        info_type=InformationType.VULNERABILITY,
        content={
            "target": "*************",
            "vulnerability": "CVE-2021-44228",
            "description": "Apache Log4j Remote Code Execution",
            "severity": "CRITICAL",
            "cvss_score": 10.0,
            "affected_service": "Apache",
            "port": 80,
            "exploit_available": True,
            "remediation": "Update Log4j to version 2.17.0 or later"
        },
        priority=InformationPriority.CRITICAL,
        tags={"*************", "log4j", "rce", "critical"},
        metadata={
            "discovery_date": datetime.now().isoformat(),
            "tool": "nuclei",
            "verified": True
        }
    )
    print(f"✅ Vulnerabilità memorizzata: {vuln_id}")
    
    # 3. Memorizza risultato comando
    print("\n3. Memorizzazione risultato comando...")
    cmd_result_id = await a0.store_information(
        source_agent="demo",
        info_type=InformationType.COMMAND_RESULT,
        content={
            "target": "*************",
            "command": "nmap -sV -sC *************",
            "output": """
Starting Nmap 7.91 ( https://nmap.org ) at 2024-01-15 10:30:00 UTC
Nmap scan report for *************
Host is up (0.001s latency).
PORT     STATE SERVICE VERSION
22/tcp   open  ssh     OpenSSH 8.2p1 Ubuntu 4ubuntu0.5
80/tcp   open  http    Apache httpd 2.4.41
443/tcp  open  https   Apache httpd 2.4.41
3306/tcp open  mysql   MySQL 8.0.32
            """,
            "exit_code": 0,
            "execution_time": 15.3
        },
        priority=InformationPriority.MEDIUM,
        tags={"*************", "nmap", "port-scan"},
        metadata={
            "execution_date": datetime.now().isoformat(),
            "agent": "a5_terminal_executor"
        }
    )
    print(f"✅ Risultato comando memorizzato: {cmd_result_id}")
    
    return a0, [target_info_id, vuln_id, cmd_result_id]

async def demo_retrieval_operations(a0, entry_ids):
    """Dimostra operazioni di recupero"""
    print("\n🔍 Demo Recupero Informazioni")
    print("=" * 50)
    
    # 1. Recupera tutte le informazioni su un target
    print("\n1. Recupero informazioni per target...")
    target_entries = await a0.retrieve_information(
        requester_agent="demo",
        tags={"*************"}
    )
    print(f"✅ Trovate {len(target_entries)} informazioni per il target")
    for entry in target_entries:
        print(f"   - {entry.info_type.value}: {entry.content.get('target', 'N/A')}")
    
    # 2. Recupera solo vulnerabilità
    print("\n2. Recupero solo vulnerabilità...")
    vuln_entries = await a0.retrieve_information(
        requester_agent="demo",
        info_type=InformationType.VULNERABILITY
    )
    print(f"✅ Trovate {len(vuln_entries)} vulnerabilità")
    for entry in vuln_entries:
        vuln = entry.content
        print(f"   - {vuln.get('vulnerability', 'N/A')}: {vuln.get('severity', 'N/A')}")
    
    # 3. Recupera informazioni correlate
    print("\n3. Recupero informazioni correlate...")
    if entry_ids:
        related_entries = await a0.get_related_information(entry_ids[0])
        print(f"✅ Trovate {len(related_entries)} informazioni correlate")
        for entry in related_entries:
            print(f"   - {entry.info_type.value}: {len(entry.tags)} tag comuni")

async def demo_statistics(a0):
    """Dimostra statistiche della memoria"""
    print("\n📊 Demo Statistiche Memoria")
    print("=" * 50)
    
    summary = await a0.get_memory_summary()
    
    print(f"📈 Statistiche Generali:")
    print(f"   - Totale entries: {summary['statistics']['total_entries']}")
    print(f"   - Dimensione cache: {summary['statistics']['cache_size']}")
    print(f"   - Hit ratio cache: {summary['cache_efficiency']}")
    print(f"   - Dimensione DB: {summary['statistics']['database_size_mb']:.2f} MB")
    print(f"   - Uptime: {summary['uptime']}")
    
    print(f"\n📊 Entries per tipo:")
    for info_type, count in summary['statistics']['entries_by_type'].items():
        print(f"   - {info_type}: {count}")
    
    print(f"\n🤖 Entries per agente:")
    for agent, count in summary['statistics']['entries_by_agent'].items():
        print(f"   - {agent}: {count}")

async def demo_cleanup(a0):
    """Dimostra operazioni di cleanup"""
    print("\n🧹 Demo Cleanup e Manutenzione")
    print("=" * 50)
    
    # Esegui task di memory management
    result = await a0.execute_task({
        "task_type": "memory_management"
    })
    
    if result.success:
        data = result.data
        print(f"✅ Cleanup completato:")
        print(f"   - Entries rimosse: {data['cleaned_entries']}")
        print(f"   - Backup creato: {data['backup_created']}")
        if data['backup_path']:
            print(f"   - Path backup: {data['backup_path']}")
    else:
        print(f"❌ Errore cleanup: {result.error}")

async def main():
    """Funzione principale demo"""
    print("🚀 Demo Completa A0 Memory Manager")
    print("=" * 60)
    
    try:
        # Demo operazioni base
        a0, entry_ids = await demo_basic_operations()
        
        # Demo recupero
        await demo_retrieval_operations(a0, entry_ids)
        
        # Demo statistiche
        await demo_statistics(a0)
        
        # Demo cleanup
        await demo_cleanup(a0)
        
        print("\n✅ Demo completata con successo!")
        
    except Exception as e:
        print(f"\n❌ Errore durante la demo: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
